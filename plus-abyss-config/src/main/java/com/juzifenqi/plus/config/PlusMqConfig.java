package com.juzifenqi.plus.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/22 2:51 下午
 */
@Data
@Component
public class PlusMqConfig {

    /**
     * 开通plus会员,如果方案中存在提额权益，发送mq消息给风控进行提额
     */
    @Value("${mq.topic.open.member}")
    private String topicOpenMember;
    @Value("${mq.gid.open.member}")
    private String pidOpenMember;
    @Value("${mq.tag.open.member.fk}")
    private String tagOpenMemberFk;

    /**
     * 取消会员发送mq信息
     */
    @Value("${mq.topic.cancel.member}")
    private String topicCancelMember;
    @Value("${mq.gid.cancel.member}")
    private String pidCancelMember;
    @Value("${mq.tag.cancel.member.fk}")
    private String tagCancelMemberFk;

    /**
     * 会员类型发送mq  memberType 1 开通 2 取消 3 过期
     */
    @Value("${mq.topic.member.type}")
    private String topicMemberType;
    @Value("${mq.gid.member.type}")
    private String pidMemberType;
    @Value("${mq.tag.member.type}")
    private String tagMemberType;
    @Value("${mq.tag.member.type.su}")
    private String tagMemberTypeSu;
    /**
     * 会员合并-重提卡新增-添加MQ类型
     */
    @Value("${mq.tag.member.type.rc}")
    private String tagMemberTypeRC;

    /**
     * 消息中心mq信息
     */
    @Value("${mq.topic.send.msg.to.center}")
    private String topicMessageCenter;
    @Value("${mq.gId.send.msg.to.center}")
    private String pidMessageCenter;
    @Value("${mq.tag.send.msg.to.center}")
    private String tagMessageCenter;

    /**
     * 风控认证审核结果通知 认证回调--桔享Plus拒就赔相关
     */
    @Value("${mq.topic.fk.auth.callback}")
    private String authCallbackTopic;
    @Value("${mq.cId.fk.auth.callback.vip.member}")
    private String authCallbackCid;
    @Value("${mq.tag.fk.auth.callback}")
    private String authCallbackTag;


    /**
     * 订单拒就赔mq信息
     */
    @Value("${mq.topic.order.plus}")
    private String orderRejTopic;
    @Value("${mq.gid.order.plus}")
    private String orderRejGid;


    /**
     * 互联订单状态变更mq
     */
    @Value("${mq.topic.order.plus.hl}")
    private String orderRejHlTopic;
    @Value("${mq.gid.order.plus.hl}")
    private String orderRejHlGid;

    /**
     * 订单 订单支付成功 开通会员mq信息
     */
    @Value("${mq.topic.order.open.card}")
    private String orderOpenTopic;
    @Value("${mq.gid.order.open.card}")
    private String orderOpenGid;

    /**
     * 开卡认证重提
     */
    @Value("${mq.topic.auth.card.open}")
    private String topicAuthCardOpen;
    @Value("${mq.gid.auth.card.open}")
    private String pidAuthCardOpen;
    @Value("${mq.tag.auth.card.open}")
    private String tagAuthCardOpen;

    /**
     * 是否可购买记录-消费风控MQ
     */
    @Value("${mq.topic.fk.canbuy.sync}")
    private String riskCanBuyTopic;
    @Value("${mq.gid.fk.canbuy.sync}")
    private String riskCanBuyGid;
    @Value("${mq.tag.fk.canbuy.sync}")
    private String riskCanBuyTag;

    @Value("${mq.gid.fk.againBring.sync}")
    private String riskAgainBringGid;


    /**
     * 未绑定借款单的降息卡会员，放入延时队列
     */
    @Value("${mq.topic.bind.check}")
    private String topicBindCheck;
    @Value("${mq.gId.bind.check}")
    private String gidBindCheck;
    @Value("${mq.tag.bind.check}")
    private String tagBindCheck;

    /**
     * 订单节点变换通知-外部渠道
     */
    @Value("${mq.topic.order.jdd}")
    private String orderJddTopic;
    @Value("${mq.gId.order.jdd}")
    private String orderJddGid;

    @Value("${mq.topic.init.orderTask}")
    private String topicContractCreate;
    private String tagContractCreate = "*";

    /**
     * 订单进采购（商品贷订单放款成功）
     */
    @Value("${mq.topic.order.purchase}")
    private String orderPurchaseTopic;
    @Value("${mq.pId.order.purchase}")
    private String orderPurchaseGid;
    @Value("${mq.tag.order.purchase}")
    private String orderPurchaseTag = "*";

    /**
     * 额度变更
     */
    @Value("${mq.topic.credit.change}")
    private String creditChangeTopic;
    @Value("${mq.gid.credit.change}")
    private String creditChangeGid;

    /**
     * 重提客群用户确认结果
     */
    @Value("${mq.topic.resubmit.confirm.result}")
    private String resubmitConfirmResultTopic;

    /**
     * 代付打款结果回调
     */
    @Value("${mq.topic.defray.back}")
    private String topicDefrayBack;
    @Value("${mq.gId.defray.back}")
    private String gidDefrayBack;
    @Value("${mq.tag.defray.back}")
    private String tagDefrayBack;

    /**
     * 支付回调支付结果
     */
    @Value("${mq.topic.pay.callback}")
    private String topicPayCallback;
    @Value("${mq.gid.pay.callback}")
    private String gidPayCallback;

    /**
     * 新支付系统回调支付结果
     */
    @Value("${mq.topic.newPay.payResult}")
    private String topicNewPayResult;
    @Value("${mq.gid.newPay.payResult}")
    private String gidNewPayResult;

    /**
     * 新支付系统代付回调结果
     */
    @Value("${mq.topic.newPay.defrayResult}")
    private String topicNewDefrayResult;
    // 会员订单gid
    @Value("${mq.gid.newPay.orderDefrayResult}")
    private String gidOrderNewDefrayResult;
    // 结算gid
    @Value("${mq.gid.newPay.settleDefrayResult}")
    private String gidSettleNewDefrayResult;

    /**
     * 会员节点变化通知
     */
    @Value("${mq.topic.plus.node.push}")
    private String topicPlusNodePush;

    /**
     * 子轩充值结果
     */
    @Value("${mq.topic.virtual.result}")
    private String topicVirtualResult;
    @Value("${mq.gid.virtual.result}")
    private String gidVirtualResult;

    /**
     * 购物返现通知分销mq
     */
    @Value("${mq.topic.plusopt.gwfx}")
    private String topicPlusOptGwfx;

    /**
     * 订单确认收货mq
     */
    @Value("${mq.topic.order.receive}")
    private String orderReceiveTopic;
    @Value("${mq.gid.order.receive}")
    private String orderReceiveGid;

    /**
     * 订单退款业务回调mq
     */
    @Value("${mq.topic.pay.refund}")
    private String topicOrderCancelFirstPay;
    @Value("${mq.cid.pay.refund}")
    private String gidOrderCancelFirstPay;

    /**
     * 合同签章完成回调mq
     */
    @Value("${mq.topic.contract.result}")
    private String topicContractResult;
    @Value("${mq.gid.contract.result}")
    private String gidContractResult;


    /**
     * 原路退款结果mq
     */
    @Value("${mq.topic.refund.result}")
    private String topicRefundResult;
    @Value("${mq.gid.refund.result}")
    private String gidRefundResult;

    /**
     * 权益发放mq：super-plus消费
     */
    @Value("${mq.topic.profits.send}")
    private String topicProfitsSend;

    /**
     * 代付打款结果.内部消费
     */
    @Value("${mq.topic.abyss.defray.result}")
    private String topicAbyssDefrayResult;
    @Value("${mq.gid.abyss.defray.result}")
    private String gidAbyssDefrayResult;

    /**
     * 发送短信
     */
    @Value("${mq.topic.msg}")
    private String topicSendMsg;

    /**
     * 结清返现、还款返现打款结果回调
     */
    @Value("${mq.topic.micro.defray.back}")
    private String topicMicroDefrayBack;
    @Value("${mq.gid.micro.defray.back}")
    private String gidMicroDefrayBack;
    @Value("${mq.tag.micro.defray.back}")
    private String tagMicroDefrayBack;

    /**
     * 会员身份过期：plus-abyss内部消费（取消待支付的后付款订单）
     */
    @Value("${mq.topic.member.expire.detail}")
    private String topicMemberDetailExpire;
    @Value("${mq.gid.member.expire.detail}")
    private String gidMemberDetailExpire;

    /**
     * 会员周期过期：plus-abyss内部消费（还款卡续费处理）
     */
    @Value("${mq.topic.member.expire.info}")
    private String topicMemberInfoExpire;
    @Value("${mq.gid.member.expire.info}")
    private String gidMemberInfoExpire;

    /**
     * 借款订单还款mq
     */
    @Value("${mq.topic.repay.back}")
    private String topicRepayBack;
    @Value("${mq.gid.repay.back}")
    private String gidRepayBack;

    /**
     * 订单中心退款mq
     */
    @Value("${mq.topic.order.refund}")
    private String topicOrderRefund;
    @Value("${mq.gid.order.refund}")
    private String gidOrderRefund;


    /**
     * 会员订单关闭通知
     */
    @Value("${mq.topic.plus.orderClose:TOPIC_PLUS_ORDER_CLOSE}")
    private String topicPlusOrderClose;

    /**
     * 会员开通发送消息给账务
     */
    @Value("${mq.topic.plus.open:TOPIC_PRD_YWZZ_EXPEDITE_NOTIFY}")
    private String topicPlusOpen;

    /**
     * 认证失效，会员退款
     */
    @Value("${mq.topic.auth.invalidation}")
    private String authInvalidationTopic;
    @Value("${mq.gid.auth.invalidation}")
    private String authInvalidationGroupId;


    /**
     * 首付支付第二次退款
     */
    @Value("${mq.topic.refund.second:TOPIC_FIRST_SECOND_REFUND}")
    private String topicSecondRefund;
    @Value("${mq.gid.refund.second:GID_FIRST_SECOND_REFUND}")
    private String gidSecondRefund;
}
