package com.juzifenqi.plus.dto.resp.admin.program;

import com.juzifenqi.plus.dto.resp.profits.PlusProModelResp;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 方案详情
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13 10:43
 */
@Data
public class ProgramDetailResp implements Serializable {

    private static final long serialVersionUID = -1708777813091137815L;

    /**
     * 方案id
     */
    private Integer id;

    /**
     * 前台方案名称
     */
    private String name;

    /**
     * 生效时间
     */
    private Date effectiveTime;

    /**
     * 划线价
     */
    private BigDecimal memberPrice;

    /**
     * 方案单价
     */
    private BigDecimal mallMobilePrice;
    /**
     * 首单首月优惠：0-关闭；1-开启
     */
    private Integer    firstOrderDiscount;

    /**
     * 规则说明图片
     */
    private String ruleDescImg;

    /**
     * 状态，1-上架；2-下架
     */
    private Integer status;

    /**
     * 首单首月价钱
     */
    private BigDecimal firstOrderPrice;

    /**
     * 方案状态， 0 未生效 1 生效
     */
    private Integer programmeStatus;

    /**
     * 方案天数
     */
    private Integer programmeDays;

    /**
     * 推荐标记 0 否  1 是
     */
    private Integer recommendedMarkup;

    /**
     * 会员权益Id
     */
    private String modelId;

    /**
     * 提额等级-和风控一致
     */
    private String grade;

    /**
     * 最低提额金额
     */
    private BigDecimal minmunAmount;

    /**
     * 跳转URL
     */
    private String jumpUrl;

    /**
     * 后台方案名称
     */
    private String backstageName;

    /**
     * 预计可省金额
     */
    private BigDecimal estimateSaveAmount;

    /**
     * 是否设置了挽回弹窗，1-是，0-否
     */
    private Integer beSetRecovery;

    /**
     * 挽回弹窗图片
     */
    private String recoveryImg;

    /**
     * 是否展示到会员主页, 1-是，0-否
     */
    private Integer showHomePage;

    /**
     * 是否展示购买记录 0 否 1 是
     */
    private Integer showBuyRecord;

    /**
     * 会员等级 1-至尊卡；2-年卡；3-月卡；4-半年卡；5-季卡
     */
    private Integer memberGrade;

    /**
     * 会员主题 1-金色；2-紫色；3-铜色；4-蓝色；5-黑色
     */
    private Integer programColor;

    /**
     * 方案唯一标识
     */
    private String signProgram;

    /**
     * 会员类型id plus-config
     */
    private Integer configId;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 营销时间：最大2880分钟（48小时），用户认证失败48小时内展示失败卡会员
     */
    private Integer showTime;

    /**
     * 营销文案
     */
    private String marketContent;

    /**
     * 省钱文案
     */
    private String amountContent;

    /**
     * 会员说明
     */
    private String userExplain;

    /**
     * 会员规则
     */
    private String  userRules;
    /**
     * 是否支持后付款 1支持 2不支持
     */
    private Integer afterPayState;

    /**
     * 支付方式列表
     */
    private List<Integer> payTypes;

    /**
     * 首期支付金额
     */
    private BigDecimal firstPayAmount;

    /**
     * 合同标识
     */
    private String  contractId;

    /**
     * 是否可续费 1_是 0_否
     */
    private Integer isRenew;

    /**
     * 续费金额
     */
    private BigDecimal renewPrice;

    /**
     * 续费开放时间，会员有效期第n天
     */
    private Integer renewOpenTime;

    /**
     * 是否展示续费弹窗
     */
    private Integer isShowFrame;

    /**
     * 续费弹窗图片
     */
    private String frameImage;

    /**
     * 弹窗类型 0_每次打开展示 1_间隔
     */
    private Integer frameType;

    /**
     * 间隔 以小时为单位
     */
    private Integer interval;

    /**
     * 备选方案
     */
    private Integer alternateProgram;

    /**
     * 续费表id
     */
    private Integer isRenewId;

    /**
     * 权益列表
     */
    private List<PlusProModelResp> list;

    /**
     * 开启权益发放类型
     *
     * @see com.juzifenqi.plus.enums.RdSendTypeEnum
     */
    private Integer rdSendType;

    /**
     * 挽留弹窗是否开启 0-关闭 1-开启
     */
    private Integer popUp;

    /**
     * 二次挽留弹窗是否开启 0-关闭 1-开启
     */
    private Integer twoPopUp;

    /**
     * 关闭挽留弹窗是否选中融担卡 0-否 1-是
     */
    private Integer rdChoose;
    /**
     * 发放节点 1_开卡即发 2_后付款支付成功发放
     */
    private Integer sendNode;

    /**
     * 渠道标识 1-宜口袋  2-桔多多
     */
    private Integer bizSource;
}
