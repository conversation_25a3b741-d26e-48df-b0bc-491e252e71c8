package com.juzifenqi.plus.dto.resp.detail.land;

import com.juzifenqi.plus.dto.resp.admin.shunt.PlusShuntSupplierContractResp;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 老落地页结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/15 10:35
 */
@Data
public class LandOldDetailResp implements Serializable {

    private static final long serialVersionUID = 6961525106477866072L;

    /**
     * 方案名称
     */
    private String     name;
    /**
     * 首单优惠
     */
    private BigDecimal firstOrderPrice;

    /**
     * 标识是否是第一次购买该方案
     */
    private Boolean    firstOrder;
    /**
     * 划线价
     */
    private BigDecimal memberPrice;
    /**
     * 售价
     */
    private BigDecimal mallMobilePrice;

    /**
     * 推荐标记 0 否  1 是
     */
    private int recommendedMarkup;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 会员权益数量
     */
    private Integer rightsNum;

    /**
     * 预计可省金额
     */
    private BigDecimal estimateSaveAmount;

    /**
     * 每日费用
     */
    private String amountPreDay;

    /**
     * 是否设置了挽回弹窗，1-是，0-否
     */
    private Integer beSetRecovery;

    /**
     * 挽回弹窗图片
     */
    private String recoveryImg;

    /**
     * 会员等级
     */
    private Integer memberGrade;

    /**
     * 方案天数
     */
    private int programmeDays;

    /**
     * 是否能买, 1能买，显示立即购买，2不能买，显示已售罄
     */
    private Integer canBuy;

    /**
     * 对应-"operate"
     */
    private Integer operate;

    /**
     * 权益信息
     */
    private List<LandModelBasicDetailResp> modelBasicInfoVos;

    /**
     * 会员权益页中的权益
     */
    private Map<String, Object> memberDetailProfits;

    /**
     * 是否展示会员节福利入口：1-是，0-否
     */
    private Integer actMemberType;

    /**
     * 会员主题 1-金色；2-紫色；3-铜色；4-蓝色；5-黑色
     */
    private Integer programColor;

    /**
     * 会员版本 1-横版；2-竖版
     */
    private Integer programVersion;

    /**
     * 是否显示会员权益页按钮 1-展示；2-不展示
     */
    private Integer showPlusButton;

    /**
     * 会员到期时间
     */
    private String endTime;

    /**
     * 专属权益到期时间
     */
    private String profitEndTime;

    /**
     * 用户登录状态 1 是登录  0是未登录
     */
    private Integer isLogin = 1;

    /**
     * 是否有重提订单(1_有,2_无) 会员合并-重提卡新增-是否有重提订单字段
     */
    private Integer hasResubmitOrder;

    /**
     * 虚拟商品信息
     */
    private List<LandVirtualProductTypeResp> virtualProfits;

    /**
     * 缓存中key失效，提示弹框状态码
     */
    private Integer showFrame;

    /**
     * 营销文案
     */
    private String marketContent;

    /**
     * 省钱文案
     */
    private String amountContent;

    /**
     * 会员说明
     */
    private String userExplain;

    /**
     * 会员规则
     */
    private String userRules;

    /**
     * 认证状态  1_已认证、2_认证中 3_授权失败 4_未认证 5_认证项即将过期 7_认证项已过期 8_准入失败,创建审批单异常 13_取消认证
     */
    private Integer authState;

    /**
     * 后付款订单号
     */
    private String orderAfterPay;

    /**
     * 是否支持后付款 1支持 2不支持
     */
    private Integer afterPayState;
    /**
     * 合同标识
     */
    private String  contractId;
    /**
     * 合同状态 1.处理中 2.签章失败 3.签章成功 4过期失效 5没有签约过
     */
    private Integer contractState;
    /**
     * 订单id
     */
    private String  orderSn;
    /**
     * 开关状态 1_开启 2_关闭
     */
    private Integer plusSwitch;

    /**
     * 是否符合 默认不符合 = 0  符合：1
     */
    private int showDiscountInfo;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 折扣价格
     */
    private BigDecimal discountPrice;

    /**
     * 结束营销时间和当前时间的相差ms数
     */
    private Long discountEndTime;

    /**
     * 延时时间
     */
    private Integer delayTime;

    /**
     * 分流合作方id-落地页返回
     */
    private Integer shuntSupplierId;

    /**
     * 分流主体名称
     */
    private String supplierName;

    /**
     * 支付方式
     */
    private List<Integer> payTypes;

    /**
     * 首付支付金额
     */
    private BigDecimal firstPayAmount;

    /**
     * 剩余支付金额
     */
    private BigDecimal surplusPayAmount;

    /**
     * 分流主体合同列表
     */
    private List<PlusShuntSupplierContractResp> contractList;
}
