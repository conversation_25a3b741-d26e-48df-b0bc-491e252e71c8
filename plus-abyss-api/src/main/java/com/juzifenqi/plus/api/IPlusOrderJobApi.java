package com.juzifenqi.plus.api;

import com.juzifenqi.plus.dto.req.OrderNoticeExecuteReq;
import com.juzifenqi.plus.dto.req.OrderRefundExecuteReq;
import com.juzifenqi.plus.dto.req.RefundExecuteReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.enums.PlusPayTypeEnum;


/**
 * 订单相关调度任务
 *
 * <AUTHOR>
 */
public interface IPlusOrderJobApi {

    /**
     * 小额月卡-续费创单任务
     */
    void renewJob(Integer limit);

    /**
     * 小额月卡-续费创单失败重试任务
     */
    void renewJobRetry(Integer limit, Integer maxTryCount);

    /**
     * 对外输出退卡申请执行
     */
    PlusAbyssResult orderRefundExecute(OrderRefundExecuteReq req);

    /**
     * 对外输出订单退卡申请结果通知
     */
    PlusAbyssResult orderNoticeExecute(OrderNoticeExecuteReq req);

    /**
     * 订单代付任务执行
     */
    void orderDefrayExecute();

    /**
     * 订单结清返现任务执行
     */
    void microDefrayPayExecute();

    /**
     * 延迟退款任务执行
     */
    void delayRefundExecute(Integer size);

    /**
     * 急速退款任务执行
     */
    void refundExecute(RefundExecuteReq req);

    /**
     * 批量取消会员订单
     */
    void batchCancelOrders();

    /**
     * 批量创建融担咨询卡
     */
    void batchCreateRdzxOrder(Integer optStatus);

    /**
     * 失效后付款创单失败的订单
     */
    void invalidOrder();

    /**
     * 会员过期
     */
    void memberExpire();

    /**
     * 通知三方入账重试
     */
    void incomeRetry(Integer size);

    /**
     * 通知三方出账重试
     */
    void outcomeRetry(Integer size);

    /**
     * 合同签署重试
     */
    void contractSignReTry(Integer size);

    /**
     * 合同上传重试
     */
    void contractUploadReTry(Integer size);

    /**
     * 订单中心状态变更mq任务
     */
    void mqRecordExecute();

    /**
     * 融担咨询卡划扣
     * <p>计划划扣日划扣/逾期划扣</p>
     */
    void rdzxDeduct(PlusPayTypeEnum payType);

    /**
     * 非融担卡延迟划扣
     */
    void delayBatchDeduct();

    /**
     * 非融担咨询卡延迟划扣短信提醒
     */
    void delayDeductSms();

    /**
     * 权益发放计划任务
     */
    void plusSendPlanTasks(Integer modelId);

    /**
     * 移动权益发放计划任务
     */
    void movePlusSendPlanTasks();

    /**
     * 原super-plus领取优惠券跑批 品牌专区+月享红包 待历史数据跑批完后可以废弃
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/3/1 13:59
     */
    void oldGrantCouponJob();

    /**
     * 修改方案价格
     */
    void plusProgramEditPrice();

    /**
     * 加入黑名单
     */
    void executePlusBlackTask(Integer state, Integer size);

    /**
     * job-处理方案ID和基础权益缓存
     */
    void handlePlusBasicProfitsCache();

    /**
     * job-后付款支付提醒
     */
    void waitPaySendSms();

    /**
     * 还款卡即将结束前n天发短信
     */
    void repayPlusRemindSms(String size);

    /**
     * 拉取还款计划
     */
    void repayPlanJob();

    /**
     * 订单重提标识查询
     */
    void resubmitFlagVerify();

    /**
     * 重提客群资匹闭单失败重试
     */
    void resubmitGroupZpCloseRetry(Integer size);

    /**
     * 重提客群主流程失败重试
     */
    void resubmitGroupRetry(Integer size, Integer index);

    /**
     * 开店重试
     */
    void openStoreRetry();

    /**
     * 单会员过期任务
     */
    void singlePlusExpire();

    /**
     * 小额月卡续费前5天发短信
     */
    void xeykRenewRemind();

    /**
     * 对账表划扣中数据重试
     */
    void deductIncomeRetry(Integer limit);

    /**
     * 还款返现job
     */
    void hkfxMicroDefrayPayJob();

    /**
     * 划扣失败预警
     */
    void deductError(String param);

    /**
     * 手动更新划扣表
     */
    void manualDeduct(String param);

    /**
     * 补偿处理未签署合同的融单卡订单
     */
    void manualHandleRdzxOrderContractJob();

    /**
     * 处理融单咨询卡签署合同补偿处理失败的记录
     */
    void makeUpHandleRdzxOrderContractJob();

    /**
     * 初始化代付新银行卡号密文
     */
    void initPastCardNoUuid(Long index, Integer size);

    /**
     * 检测代付新银行卡号密文
     * @param size
     */
    void inspectPastCardNoUuid(Integer size);


    /**
     * 初始化AI外呼日志记录手机号密文
     */
    void initAiOutboundMobileJob(Integer size);

    /**
     * 会员订单重新退款(用于首付支付第二次退款失败，手动重试处理)
     */
    void orderRetryRefund(Long refundInfoId);
}
