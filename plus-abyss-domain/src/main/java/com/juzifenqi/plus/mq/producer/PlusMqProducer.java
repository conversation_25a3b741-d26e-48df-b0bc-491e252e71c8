package com.juzifenqi.plus.mq.producer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.SendResult;
import com.groot.utils.exception.LogUtil;
import com.juzi.smsgroup.vo.SmsMsgV2Dto;
import com.juzifenqi.mq.producer.normal.AbstractNormalMqClient;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.config.PlusMqConfig;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.NewGradeEnum;
import com.juzifenqi.plus.module.asserts.model.event.PlusActRebateReqEvent;
import com.juzifenqi.plus.module.asserts.repository.external.event.ExternalLiftAmountOpenEvent;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo;
import com.juzifenqi.plus.module.common.IIMRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.MemberMqEntity;
import com.juzifenqi.plus.module.order.model.event.PlusNodePushEvent;
import com.juzifenqi.plus.module.order.model.event.order.OrderRefundSecondEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderDefrayResultMqEvent;
import com.juzifenqi.plus.module.order.model.event.resubmitgroup.ResubmitGroupUserConfirmEvent;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 发送mq的入口类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/22 2:29 下午
 */
@Component
@Slf4j
public class PlusMqProducer extends AbstractNormalMqClient {

    @Autowired
    private PlusMqConfig     mqConfig;
    @Autowired
    private IIMRepository    imRepository;
    @Autowired
    private ConfigProperties configProperties;

    /**
     * 开通plus会员,如果方案中存在提额权益，发送mq消息给风控进行提额
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2021/6/22 2:30 下午
     */
    public SendResult sendOpenPlusTeInfo(ExternalLiftAmountOpenEvent params, String orderSn,
            Integer memberId) {
        log.info(
                "开通会员提额推送风控业务MQ消息发送开始, orderId: {}, topic: {}, tag: {}, message: {}",
                orderSn, mqConfig.getTopicOpenMember(), mqConfig.getTagOpenMemberFk(),
                JSONObject.toJSONString(params));
        SendResult sendResult = sendDelayMessageResult(JSONObject.toJSONString(params),
                mqConfig.getTopicOpenMember(), mqConfig.getTagOpenMemberFk(), 3000);
        log.info("开通会员提额推送风控业务MQ消息发送结束, orderId: {}, 发送状态: {}", orderSn,
                sendResult);
        if (Objects.isNull(sendResult)) {
            log.info("用户ID={}开通会员orderSn={}提额推送风控mq失败", memberId, orderSn);
        }
        return sendResult;
    }

    /**
     * 取消会员 发送mq消息
     */
    public void sendCancelPlusInfo(String channel, String orderSn, Integer memberId,
            Integer configId) {
        try {
            Map<String, String> params = new HashMap<>(4);
            params.put("custId", String.valueOf(memberId));
            params.put("channelId", channel);
            params.put("orderNo", orderSn);
            params.put("memberRaise", "true");
            if (configId == JuziPlusEnum.SUCCESS_CARD.getCode()) {
                params.put("raiseOperateType", NewGradeEnum.COMMON_QUOTA_GRADE.getNewGrade());
            }
            log.info("取消会员通知风控MQ消息发送开始, orderId: {}, topic: {}, tag: {}, message: {}",
                    orderSn, mqConfig.getTopicCancelMember(), mqConfig.getTagCancelMemberFk(),
                    JSON.toJSONString(params));
            boolean resultMp = sendMessage(JSON.toJSONString(params),
                    mqConfig.getTopicCancelMember(), mqConfig.getTagCancelMemberFk());
            log.info("取消会员通知风控MQ消息发送结束, orderId: {}, 发送状态: {}", orderSn,
                    resultMp);
        } catch (Exception e) {
            imRepository.sendImMessage(
                    "取消会员通知风控MQ消息发送异常，用户userID=" + memberId + ",orderNo=" + orderSn
                            + "\n" + e.getMessage());
            LogUtil.printLog("取消会员推送风控mq异常", e);
        }
    }

    /**
     * 发送push到消息中心
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2021/6/22 2:35 下午
     */
    public void sendInfoToMsgCenter(Map<String, Object> param, Integer memberId) {
        log.info("用户ID:{} 推送消息中心MQ开始参数:{}", memberId, JSONObject.toJSONString(param));
        Boolean send = sendMessage(JSONObject.toJSONString(param), mqConfig.getTopicMessageCenter(),
                mqConfig.getTagMessageCenter());
        log.info("发送PUSH，用户ID=" + memberId + "，推送消息中心MQ结束, 发送状态sendResult=" + send);
    }


    /**
     * 通知消息中心-common
     */
    public void sendPushMsg(Map<String, Object> params) {
        //发送PUSH消息
        log.info("{}:推送消息中心MQ开始,topic: {}, tag: {}, message: {}", params.get("flag"),
                mqConfig.getTopicMessageCenter(), mqConfig.getTagMessageCenter(),
                JSONObject.toJSONString(params));
        Boolean send = sendMessage(JSONObject.toJSONString(params),
                mqConfig.getTopicMessageCenter(), mqConfig.getTagMessageCenter());
        log.info("{}:推送消息中心MQ结束，发送状态sendResult:{}", params.get("flag"), send);
    }


    /**
     * 根据付费会员类型发送mq  memberType 1 开通 2 取消 3 过期
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2021/6/22 2:46 下午
     */
    public void sendMqNotice(MemberMqEntity memberMqDto, String tag) {
        try {
            log.info("根据会员类型推送MQ消息发送开始, topic: {}, tag: {}, message: {}",
                    mqConfig.getTopicMemberType(), tag, JSONObject.toJSONString(memberMqDto));
            SendResult sendResult = sendDelayMessageResult(JSONObject.toJSONString(memberMqDto),
                    mqConfig.getTopicMemberType(), tag, 500);
            log.info("根据会员类型推送MQ消息发送结束,发送状态: {}",
                    JSONObject.toJSONString(sendResult));
            if (sendResult == null) {
                log.info("根据会员类型推送mq失败memberMqDto={}", memberMqDto);
            }
        } catch (Exception e) {
            LogUtil.printLog("根据会员类型推送mq异常", e);
        }
    }

    /**
     * 开通认证失败卡，重提认证通知认证中心
     */
    public void sendAuthResubmit(Map<String, Object> map) {
        try {
            String body = JSONObject.toJSONString(map);
            log.info("开卡认证重提推送MQ消息发送开始, topic: {}, tag: {}, message: {}",
                    mqConfig.getTopicAuthCardOpen(), mqConfig.getTagAuthCardOpen(), body);
            boolean resultMp = sendMessage(body, mqConfig.getTopicAuthCardOpen(),
                    mqConfig.getTagAuthCardOpen());
            log.info("开卡认证重提推送MQ消息发送结束,发送状态: {}", resultMp);
            if (!resultMp) {
                log.info("开卡认证重提推送mq失败memberMqDto={}", body);
            }
        } catch (Exception e) {
            LogUtil.printLog("开卡认证重提推送mq异常", e);
        }
    }

    /**
     * 未绑定借款单的降息卡会员，X分钟后会发起退会员
     *
     * <AUTHOR>
     * @date 2022/8/18 16:35
     */
    public void unBindLoanOrderPush(String msg, Integer memberId) {
        log.info("未绑定借款单的降息卡会员加入延时队列开始:param={}，memberId={}", msg, memberId);
        int delayTime = configProperties.bindTimeOutMin * 60 * 1000;
        log.info("延时队列设置延时时间为={}", delayTime);
        boolean send = sendDelayMessage(msg, mqConfig.getTopicBindCheck(),
                mqConfig.getTagBindCheck(), delayTime);
        log.info("未绑定借款单的降息卡会员加入延时队列结束:memberId={},status:{}", memberId, send);
    }

    /**
     * 通知合同系统生成合同
     */
    public boolean sendGenerateContract(String body) {
        try {
            log.info("通知合同系统生成合同请求参数 message:{}", body);
            return sendMessage(body, mqConfig.getTopicContractCreate(),
                    mqConfig.getTagContractCreate());
        } catch (Exception e) {
            LogUtil.printLog(e, "通知合同系统生成合同MQ异常");
            return false;
        }
    }

    /**
     * 发送支付代付接口回调同步告知会员系统打款成功/失败结果，内部使用，本服务自己消费
     */
    public void sendDefrayResult(PlusOrderDefrayResultMqEvent event) {
        //发送PUSH消息
        String body = JSONObject.toJSONString(event);
        log.info("发送支付代付接口回调告知会员系统打款结果,topic: {}, tag: *, message: {}",
                mqConfig.getTopicAbyssDefrayResult(), body);
        Boolean send = sendMessage(body, mqConfig.getTopicAbyssDefrayResult(), "*");
        log.info("发送支付代付接口回调告知会员系统打款结果结束，发送状态sendResult:{}", send);
    }

    /**
     * 发送短信
     */
    public void sendMsg(SmsMsgV2Dto dto) {
        try {
            String message = JSONObject.toJSONString(dto);
            log.info("发送短信 message:{}", message);
            sendMessage(message, mqConfig.getTopicSendMsg(), "*");
        } catch (Exception e) {
            LogUtil.printLog(e, "发送短信MQ异常");
        }
    }

    /**
     * 发送延迟短信
     */
    public void sendDelayMsg(SmsMsgV2Dto dto, Integer delayTime) {
        try {
            String message = JSONObject.toJSONString(dto);
            log.info("发送延迟短信 message:{},延迟时间:{}ms", message, delayTime);
            sendDelayMessage(message, mqConfig.getTopicSendMsg(), "*", delayTime);
        } catch (Exception e) {
            LogUtil.printLog(e, "发送延迟短信MQ异常");
        }
    }


    /**
     * 会员身份过期
     */
    public void sendMemberDetailExpire(List<String> orderSns) {
        try {
            String message = JSONObject.toJSONString(orderSns);
            log.info("会员身份MQ发送:{}", message);
            sendMessage(message, mqConfig.getTopicMemberDetailExpire(), "*");
        } catch (Exception e) {
            LogUtil.printLog(e, "会员身份MQ发送异常");
        }
    }

    /**
     * 会员周期过期
     */
    public void sendMemberInfoExpire(MemberPlusInfoPo po) {
        try {
            String message = JSONObject.toJSONString(po);
            log.info("会员周期MQ发送:{}", message);
            sendMessage(message, mqConfig.getTopicMemberInfoExpire(), "*");
        } catch (Exception e) {
            LogUtil.printLog(e, "会员周期MQ发送异常");
        }
    }

    /**
     * 发送会员节点消息
     */
    public String sendPlusNodePush(PlusNodePushEvent event) {
        try {
            String message = JSONObject.toJSONString(event);
            log.info("发送会员节点消息 message:{}", message);
            SendResult sendResult = sendMessageResult(message, mqConfig.getTopicPlusNodePush(),
                    "*");
            return sendResult.getMessageId();
        } catch (Exception e) {
            LogUtil.printLog(e, "发送会员节点消息MQ异常");
            return null;
        }
    }

    /**
     * 发送重提客群确认结果
     */
    public String sendResubmitConfirmResult(ResubmitGroupUserConfirmEvent event) {
        try {
            String message = JSONObject.toJSONString(event);
            log.info("发送重提客群确认结果请求参数 message:{}", message);
            SendResult sendResult = sendMessageResult(message,
                    mqConfig.getResubmitConfirmResultTopic(), "*");
            return sendResult.getMessageId();
        } catch (Exception e) {
            LogUtil.printLog(e, "发送重提客群确认结果MQ异常");
            return null;
        }
    }

    /**
     * 返现数据推送到分销
     */
    public String sendCashbackPush(PlusActRebateReqEvent plusActRebateReqEvent) {
        try {
            String message = JSONObject.toJSONString(plusActRebateReqEvent);
            log.info("发送返现到分销 message:{}", message);
            SendResult sendResult = sendMessageResult(message, mqConfig.getTopicPlusOptGwfx(), "*");
            return sendResult.getMessageId();
        } catch (Exception e) {
            LogUtil.printLog(e, "发送购物返现到分销MQ异常");
            return null;
        }
    }

    /**
     * 撤销返现
     */
    public String rollbackCashbackPush(PlusActRebateReqEvent plusActRebateRollbackEvent) {
        try {
            String message = JSONObject.toJSONString(plusActRebateRollbackEvent);
            log.info("撤销返现-发送返现到分销 message:{}", message);
            SendResult sendResult = sendMessageResult(message, mqConfig.getTopicPlusOptGwfx(), "*");
            return sendResult.getMessageId();
        } catch (Exception e) {
            LogUtil.printLog(e, "撤销返现到分销MQ异常");
            return null;
        }
    }


    public String sendThirdSourceOrderClose(PlusOrderCancelEvent cancelEvent) {
        try {
            String message = JSONObject.toJSONString(cancelEvent);
            log.info("会员订单关闭消息通知 message:{}", message);
            SendResult sendResult = sendMessageResult(message, mqConfig.getTopicPlusOrderClose(), "*");
            return sendResult.getMessageId();
        } catch (Exception e) {
            LogUtil.printLog(e, "会员订单关闭消息通知MQ异常");
            return null;
        }
    }


    /**
     * 开通会员发送新MQ消息给账务
     */
    public void sendPlusOpenMQ(Integer userId) {
        JSONObject event = new JSONObject();
        event.put("userId", userId);
        event.put("accelerationType", 2); //2-现金贷
        event.put("memberType", 1); // 1-开通
        event.put("privilegeTag", 2);
        event.put("businessLine", 1); //1-会员
        event.put("loanPriority", 1); //1-高
        String body = JSONObject.toJSONString(event);
        log.info("开通会员发送新MQ,topic: {}, tag: *, message: {}", mqConfig.getTopicPlusOpen(), body);
        try {
            Boolean send = sendDelayMessage(body, mqConfig.getTopicPlusOpen(), "*", 500);
            log.info("开通会员发送新MQ，发送状态sendResult:{}", send);
        } catch (Exception e) {
            log.error("开通会员发送新MQ, 发送异常", e);
        }
    }


    /**
     * 会员退款第二次退款请求
     */
    public void sendSecondRefundMq(OrderRefundSecondEvent event) {
        String body = JSONObject.toJSONString(event);
        log.info("首期支付订单第二次退款MQ,topic: {}, tag: *, message: {}", mqConfig.getTopicSecondRefund(), body);
        try {
            Boolean send = sendDelayMessage(body, mqConfig.getTopicSecondRefund(), "*", 500);
            log.info("首期支付订单第二次退款MQ，发送状态sendResult:{}", send);
        } catch (Exception e) {
            log.error("开通会员发送新MQ, 发送异常", e);
            imRepository.sendImMessage("首期支付订单第二次退款MQ,发送消息异常");
        }
    }

}
