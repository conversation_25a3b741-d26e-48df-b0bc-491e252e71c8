package com.juzifenqi.plus.module.settle.repository.impl;

import com.juzifenqi.plus.dto.req.admin.settle.SettlePendingQueryReq;
import com.juzifenqi.plus.module.settle.model.contract.PlusOrderSettleItemPendingRepository;
import com.juzifenqi.plus.module.settle.model.contract.entity.PlusOrderSettleItemPendingAdminEntity;
import com.juzifenqi.plus.module.settle.model.contract.entity.PlusOrderSettleItemPendingEntity;
import com.juzifenqi.plus.module.settle.repository.convertor.IPlusOrderSettleItemPendingRepositoryConverter;
import com.juzifenqi.plus.module.settle.repository.dao.IPlusOrderSettleItemPendingMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会员订单待结算明细表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/3 16:18
 */
@Service
@Slf4j
public class PlusOrderSettleItemPendingRepositoryImpl implements
        PlusOrderSettleItemPendingRepository {

    private final IPlusOrderSettleItemPendingRepositoryConverter converter = IPlusOrderSettleItemPendingRepositoryConverter.instance;

    @Autowired
    private IPlusOrderSettleItemPendingMapper pendingMapper;

    /**
     * 保存待结算明细
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderSettleItemPending(List<PlusOrderSettleItemPendingEntity> saveEntities) {
        pendingMapper.batchInsert(converter.toPlusOrderSettleItemPendingPoList(saveEntities));
    }

    /**
     * 按日期查询待结算主体信息
     */
    @Override
    public List<PlusOrderSettleItemPendingEntity> getSettlePendingSupplierByDate(String beginTime,
            String endTime, Integer isCreateSettle) {
        return converter.toPlusOrderSettleItemPendingEntityList(
                pendingMapper.getSettlePendingSupplierByDate(beginTime, endTime, isCreateSettle));
    }

    /**
     * 通过订单号查询待结算明细
     */
    @Override
    public List<PlusOrderSettleItemPendingEntity> getSettlePendingItemsByOrderSn(String orderSn) {
        return converter.toPlusOrderSettleItemPendingEntityList(
                pendingMapper.getSettlePendingItemsByOrderSn(orderSn));
    }

    @Override
    public List<PlusOrderSettleItemPendingEntity> getByOrderSnAndSerialNo(String orderSn, String serialNo) {
        return converter.toPlusOrderSettleItemPendingEntityList(
                pendingMapper.getByOrderSnAndSerialNo(orderSn, serialNo));
    }

    /**
     * 按日期和主体查询待结算明细
     */
    @Override
    public List<PlusOrderSettleItemPendingEntity> getSettlePendingByDate(String beginTime,
            String endTime, Integer shuntSupplierId, Integer separateSupplierId,
            Integer isCreateSettle, Integer channelId) {
        return converter.toPlusOrderSettleItemPendingEntityList(
                pendingMapper.getSettlePendingByDate(beginTime, endTime, shuntSupplierId,
                        separateSupplierId, isCreateSettle, channelId));
    }

    /**
     * 分页查询待结算列表
     */
    @Override
    public List<PlusOrderSettleItemPendingAdminEntity> getPendingPageList(
            SettlePendingQueryReq req) {
        return pendingMapper.getPendingPageList(req);
    }

    /**
     * 查询待结算总条数
     */
    @Override
    public Integer pendingPageListCount(SettlePendingQueryReq req) {
        return pendingMapper.pendingPageListCount(req);
    }
}
