package com.juzifenqi.plus.module.market.model.contract.entity;

import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierContractEntity;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 借款首页营销返回结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/23 14:53
 */
@Data
@Accessors(chain = true)
public class PlusLoanMarketEntity {

    /**
     * 营销会员类型标识 see ShowTypeEnum
     */
    private Integer showType = 0;

    /**
     * 营销方案id
     */
    private Integer programId;

    /**
     * 营销方案名称
     */
    private String programName;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 营销方案价格
     */
    private BigDecimal mallMobilePrice;

    /**
     * 是否需要折扣弹窗（新折扣）
     */
    private boolean needDiscountAlert;

    /**
     * 提额后可借款金额
     * <p>通过营销的方案提额等级调用额度试算接口试算返回</p>
     */
    private BigDecimal vipCanWithdralAmount;

    /**
     * 分流方id
     */
    private Integer shuntSupplierId;

    /**
     * 是否符合折扣 默认不符合 = 0  符合：1
     */
    private Integer showDiscountInfo = 0;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 折扣结束营销时间和当前时间的相差ms数
     */
    private Long discountEndTime;

    /**
     * 折扣价格
     */
    private BigDecimal discountPrice;

    /**
     * 展示文案内容, 0=不展示；1=开通文案；2=提额文案；3=息费折扣文案; 4=风控接口异常 5=新人礼包-提额文案 6=新人礼包-加速文案 7=方案无提额项
     */
    private Integer showContent;

    /**
     * 风控可提额度
     * <p>风控返回</p>
     */
    private BigDecimal raiseAmount;

    /**
     * 7天内购买提额会员标识 0 否，1是
     */
    private Integer buyQuotaFlag = 0;

    /**
     * 弹窗 1：样式A（默认） 2 样式B
     * <p>梳理super-plus后发现目前没有对此字段赋值的逻辑，默认1</p>
     */
    private Integer popover = 1;

    /**
     * 是否支持后付款 1支持 2不支持
     */
    private Integer afterPayState;

    /**
     * 提额卡广告：1：展示（默认），2：不展示
     * <p>桔策返回</p>
     */
    private Integer advertisement;

    /**
     * 提额认证页是否开启  1_开启 2_关闭
     * <p>默认关闭，该功能已在本次重构去除，保留字段兼容前端逻辑</p>
     */
    private Integer raiseCreditSwitch = 2;

    /**
     * 点击确认调整按钮（降低额度）标识，0=未点击 1=已点击，默认未点击
     */
    private Integer reduceQuotaFlag = 0;

    /**
     * 点击确认放弃借款标识，0=未点击 1=已点击，默认未点击
     */
    private Integer waiveLoanFlag = 0;

    /**
     * 加速卡重提客群标识 0=不是 1=是，默认不是
     */
    private Integer resubmitCustomerGroupFlag = 0;

    /**
     * 用户id尾号 (0双数，1单数)
     */
    private Integer tailNumber;

    /**
     * 会员按钮 0：无按钮 ，1：左， 2：右（默认）
     */
    private Integer vipButton;

    /**
     * 7天内开通过桔享、固额、加速的其中一个并且桔策返回【点击重提客群-确认降额按钮】需要要二次弹窗，才赋值此字段
     */
    private BigDecimal sevenOpenOrderAmount;

    /**
     * 重提客群借款订单号
     */
    private String orderSn;

    /**
     * 延时划扣时间，单位：分钟
     */
    private Integer delayTime;

    /**
     * 资方id，目前用于前端在重提客群调整确认页试算每一期应还多少钱
     */
    private Integer capitalId;

    /**
     * 重提客群调整后借款金额，用于前端在重提客群调整确认页展示
     */
    private BigDecimal resubmitGroupOrderAmount;

    /**
     * 重提客群调整后借款期数，用于前端在重提客群调整确认页展示
     */
    private Integer resubmitGroupOrderPeriods;

    /**
     * 营销加速卡的借款订单排队时间
     */
    private Integer blockTime;

    /**
     * 借款订单状态
     */
    private Integer orderState;

    /**
     * 放款成功轮播
     */
    private List<LoanSuccessOrderEntity> loanRecords;

    /**
     * 借款首页营销其他卡内容
     */
    private MarketingCardEntity otherCardVo;

    /**
     * 分流主体名称
     */
    private String supplierName;

    /**
     * 分流主体合同列表
     */
    private List<PlusShuntSupplierContractEntity> contractList;

    /**
     * 样式预留字段
     */
    private String styleCode;

    /**
    * 会员权益
    */
    private List<PlusModelEntity> plusRightsList;

    /**
     * 不营销原因
     */
    private String noMarketReason;

    /**
     * 支付方式列表
     */
    private List<Integer> payTypes;

    /**
     * 首付支付金额
     */
    private BigDecimal firstPayAmount;

}

