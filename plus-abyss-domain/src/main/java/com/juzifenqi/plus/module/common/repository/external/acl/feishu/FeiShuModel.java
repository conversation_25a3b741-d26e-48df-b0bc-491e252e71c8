package com.juzifenqi.plus.module.common.repository.external.acl.feishu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.http.OKHttp3Utils;
import com.groot.utils.http.OkHttpClientEnum;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.module.common.repository.external.acl.feishu.FeishuTextReq.Content;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.util.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 飞书发送消息model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/5 14:28
 */
@Component
@Slf4j
public class FeiShuModel {

    @Autowired
    private ConfigProperties configProperties;

    /**
     * 发送文本消息 <a href="https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot">...</a>
     */
    public void sendTextMsg(String message) {
        try {
            if (StringUtils.hasText(configProperties.alertExclude)) {
                String[] split = configProperties.alertExclude.split(",");
                if (Arrays.stream(split).anyMatch(message::contains)) {
                    log.info("message:{}, 命中排除的报警信息不进行报警", message);
                    return;
                }
            }
            FeishuTextReq req = new FeishuTextReq();
            long timeMillis = System.currentTimeMillis() / 1000;
            // 时间戳
            req.setTimestamp(timeMillis);
            // 签名
            req.setSign(GenSign(configProperties.feishuSecret, timeMillis));
            // 消息内容
            String contentMsg = "<at user_id=\"all\">所有人</at>plus-abyss错误信息: " + message;
            Content content = new Content();
            content.setText(contentMsg);
            req.setContent(content);
            String requestBoyd = JSON.toJSONString(req);
            log.info("发送飞书消息入参：{}", requestBoyd);
            JSONObject jsonObject = OKHttp3Utils.postByJson(configProperties.feishuUrl, null,
                    requestBoyd, OkHttpClientEnum.FIVE_SECOND, 0);
            log.info("发送飞书消息返回：{}", jsonObject == null ? null : jsonObject.toJSONString());
        } catch (Exception e) {
            log.info("发送飞书消息异常", e);
        }
    }



    /**
     * 发送业务报警
     * api文档: <a href="https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot">...</a>
     *
     * @param message 报警信息
     * @param hookUrl 飞书机器人地址
     * @param noticeUser 通知用户 key: userId或openId, value: 飞书用户名
     */
    public void sendBizAlertMsg(String message, String hookUrl, Map<String, String> noticeUser) {
        try {
            String user = "<at user_id=\"all\">所有人</at> \n";
            if (noticeUser != null && !noticeUser.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                noticeUser.forEach((key, value) -> {
                    sb.append("<at user_id=\"").append(key).append("\">").append(value).append("</at> ");
                });
                user = sb.append(" \n").toString();
            }
            FeishuTextReq req = new FeishuTextReq();
            long timeMillis = System.currentTimeMillis() / 1000;
            // 时间戳
            req.setTimestamp(timeMillis);
            // 签名
            req.setSign(GenSign(configProperties.feishuSecret, timeMillis));
            // 消息内容
            Content content = new Content();
            content.setText(user + message);
            req.setContent(content);
            String requestBoyd = JSON.toJSONString(req);
            log.info("发送飞书业务报警入参：{}", requestBoyd);
            JSONObject jsonObject = OKHttp3Utils.postByJson(hookUrl, null,
                    requestBoyd, OkHttpClientEnum.FIVE_SECOND, 0);
            log.info("发送飞书业务报警返回：{}", jsonObject == null ? null : jsonObject.toJSONString());
        } catch (Exception e) {
            log.info("发送飞书业务报警异常", e);
        }
    }


    /**
     * 生成签名
     *
     * @param secret 秘钥
     * @param timestamp 时间戳：距当前时间不超过 1 小时（3600 秒）
     */
    private static String GenSign(String secret, long timestamp) throws Exception {
        // 把timestamp+"\n"+密钥当做签名字符串
        String stringToSign = timestamp + "\n" + secret;
        // 使用HmacSHA256算法计算签名
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(new byte[]{});
        return new String(Base64.encodeBase64(signData));
    }

    public static void main(String[] args) {
        FeiShuModel feiShuModel = new FeiShuModel();
        feiShuModel.configProperties = new ConfigProperties();
        feiShuModel.configProperties.alertExclude = "";
        feiShuModel.configProperties.feishuSecret = "3z1yPuAQWlNLD3bVL1bbCh";
        feiShuModel.configProperties.feishuUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/07124786-fb7a-4ff8-9190-827be087962e";
        String text = "航纳会员" +
                " " +
                "支付通道商户号: " +
                "660301065412H7Q" +
                ", " +
                "此商户号可用余额小于xx元, 请及时充值!";
        feiShuModel.sendBizAlertMsg(text, feiShuModel.configProperties.feishuUrl, null);
    }

}
