package com.juzifenqi.plus.module.order.api.impl;

import com.juzifenqi.plus.api.IPlusSupplierJobApi;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dubbo.DubboService;
import com.juzifenqi.plus.module.common.application.IPlusShuntApplication;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * Description: 会员主体相关业务处理
 *
 * <AUTHOR>
 * @date created on 2025/6/19
 */
@Slf4j
@DubboService
public class PlusSupplierJobApiImpl implements IPlusSupplierJobApi {

    @Resource
    private IPlusShuntApplication shuntApplication;


    @Override
    public PlusAbyssResult<Boolean> plusSupplierLimitAlarmJob() {
        return PlusAbyssResult.success(shuntApplication.plusSupplierLimitAlarm());
    }
}