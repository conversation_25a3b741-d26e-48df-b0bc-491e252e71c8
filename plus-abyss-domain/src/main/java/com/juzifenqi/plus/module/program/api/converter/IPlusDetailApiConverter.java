package com.juzifenqi.plus.module.program.api.converter;

import com.juzifenqi.plus.dto.resp.detail.land.LandDetailResp;
import com.juzifenqi.plus.dto.resp.detail.land.LandOldDetailResp;
import com.juzifenqi.plus.dto.resp.detail.land.LandVirtualProductTypeResp;
import com.juzifenqi.plus.dto.resp.detail.profit.ProfitDetailResp;
import com.juzifenqi.plus.dto.resp.detail.profit.ProfitMergeDetailResp;
import com.juzifenqi.plus.dto.resp.detail.profit.ProfitOldDetailResp;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitMergeDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitOldDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandOldDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandVirtualProductTypeEntity;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

/**
 * 会员权益页/落地页转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/14 09:33
 */
@Mapper
public interface IPlusDetailApiConverter {

    IPlusDetailApiConverter instance = Mappers.getMapper(IPlusDetailApiConverter.class);

    LandDetailResp toLandDetailResp(LandDetailEntity entity);

    ProfitDetailResp toProfitDetailResp(ProfitDetailEntity entity);

    ProfitMergeDetailResp toProfitMergeDetailResp(ProfitMergeDetailEntity entity);

    ProfitOldDetailResp toProfitOldDetailResp(ProfitOldDetailEntity entity);

    LandOldDetailResp toLandOldDetailResp(LandOldDetailEntity entity);

    List<LandVirtualProductTypeResp> toLandVirtualProductTypeRespList(
            List<LandVirtualProductTypeEntity> entityList);


    @AfterMapping
    default void ofProgramDetailResp(@MappingTarget LandOldDetailResp resp, LandOldDetailEntity entity) {
        if (null != entity && !CollectionUtils.isEmpty(entity.getPayTypes())) {
            if (entity.getPayTypes().contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                //计算剩余支付金额
                if (null != entity.getMallMobilePrice() && null != entity.getFirstPayAmount()) {
                    if ( null != entity.getDiscountRate() ) {
                        //有折扣
                        //首付剩余支付金额
                        BigDecimal surplusPayAmount = entity.getMallMobilePrice().multiply(entity.getDiscountRate()).setScale(0, RoundingMode.FLOOR).subtract(entity.getFirstPayAmount());
                        resp.setSurplusPayAmount(surplusPayAmount);
                    } else {
                        //无折扣
                        BigDecimal surplusPayAmount = entity.getMallMobilePrice().subtract(entity.getFirstPayAmount());
                        resp.setSurplusPayAmount(surplusPayAmount);
                    }
                }
            }
        }
    }
}
