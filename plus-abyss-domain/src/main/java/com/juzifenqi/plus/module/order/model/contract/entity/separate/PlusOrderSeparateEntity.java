package com.juzifenqi.plus.module.order.model.contract.entity.separate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 新增清分主体入参
 *
 * <AUTHOR>
 */
@Data
public class PlusOrderSeparateEntity implements Serializable {
    private static final long serialVersionUID = 42L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 会员单号
     */
    private String orderSn;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 桔子用户ID
     */
    private Integer userId;

    /**
     * 请求支付流水号
     */
    private String applySerialNo;

    /**
     * 分流主体id
     */
    private Integer shuntSupplierId;

    /**
     * 是否需要清分 1_否 2_是
     */
    private Integer separateEnableState;

    /**
     * 是否需要结算 1_否 2_是
     */
    private Integer settleEnableState;

    /**
     * 分账总金额
     */
    private BigDecimal totalSeparateAmount;

    /**
     * 银行卡id
     */
    private Long bankCardId;

    /**
     * 业务场景
     */
    private String businessScene;

    /**
     * 支付流水号
     */
    private String paySerialNo;

    /**
     * 分账状态 1_分账处理中 2_分账成功 3_分账失败
     */
    private Integer separateState;

    /**
     * 支付回调时间
     */
    private Date payCallbackTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date    updateTime;
    /**
     * 订单支付行为
     *
     * @see com.juzifenqi.plus.enums.OrderPayActionEnum
     */
    private Integer orderPayAction;

    /**
     * 主动支付时的支付流水号
     */
    private String serialNo;

    /**
     * 分账明细
     */
    private List<PlusOrderSeparateItemEntity> items;
}
