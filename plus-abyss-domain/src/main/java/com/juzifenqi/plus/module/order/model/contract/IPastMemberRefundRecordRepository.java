package com.juzifenqi.plus.module.order.model.contract;

import com.juzifenqi.plus.module.order.model.contract.entity.PastMemberRefundRecordEntity;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IPastMemberRefundRecordRepository {

    /**
     * 保存代付打款任务
     */
    void save(PastMemberRefundRecordEntity pastMemberRefundRecord);

    /**
     * 是否有待处理、处理中、打款成功、仅取消订单未打款的打款任务
     */
    boolean hasIngEnd(String orderSn);

    /**
     * 获取待打款的任务
     */
    List<PastMemberRefundRecordEntity> getToBeRefundList(List<Integer> businessTypes);

    /**
     * 按主键批量修改为处理中
     */
    void batchProcessing(List<Integer> ids);

    /**
     * 更改状态
     */
    void updateState(PastMemberRefundRecordEntity entity);


    PastMemberRefundRecordEntity getById(Integer id);

    /**
     * 按支付流水号获取
     */
    PastMemberRefundRecordEntity getBySerialNumber(String serialNumber);

    /**
     * 按会员单号获取
     */
    PastMemberRefundRecordEntity getByOrderSn(String orderSn);

    /**
     * Load查询
     */
    PastMemberRefundRecordEntity loadPastMemberRefundRecord(Integer id);

    /**
     * 根据代付业务流水号获取
     */
    PastMemberRefundRecordEntity getByDefraySerialNo(String defraySerialNo);

    /**
     * 获取代付打款空新银行卡号密文数据
     */
    List<PastMemberRefundRecordEntity> getWithEmptyBankAccountNoUuid(Long lastId, Integer size);

    /**
     * 获取代付打款有新银行卡号密文数据
     */
    List<PastMemberRefundRecordEntity> getWithCardNoUuid(Long lastId, Integer size);

    /**
     * 批量更新代付打款任务新银行卡号密文
     */
    void updateBatchCardNoUuid(List<PastMemberRefundRecordEntity> refundRecordEntities);

}
