package com.juzifenqi.plus.module.order.model.event.order;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PlusOrderPayCallbackEvent {

    /**
     * 用户ID
     */
    public Integer memberId;

    /**
     * 会员订单号
     */
    public String orderSn;

    /**
     * 借款单号
     */
    private String loanOrderSn;

    /**
     * 支付流水号
     */
    public String serialNumber;

    /**
     * 支付状态 S：支付成功 F：支付失败 QS：支付成功&扣额度成功 QF：支付成功&扣额度失败
     */
    private String payStatus;

    /**
     * 入口标识 1=开通会员mq 2=支付成功mq
     */
    private Integer flag;

    /**
     * 订单状态
     */
    public Integer orderState;


}
