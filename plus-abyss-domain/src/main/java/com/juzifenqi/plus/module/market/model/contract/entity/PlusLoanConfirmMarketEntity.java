package com.juzifenqi.plus.module.market.model.contract.entity;

import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierContractEntity;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 确认借款页营销返回结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/11 10:38
 */
@Data
@Accessors(chain = true)
public class PlusLoanConfirmMarketEntity {

    /**
     * 营销会员类型标识 see ShowTypeEnum
     */
    private Integer showType = 0;

    /**
     * 营销方案id
     */
    private Integer programId;

    /**
     * 营销方案名称
     */
    private String programName;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 营销方案价格
     */
    private BigDecimal mallMobilePrice;

    /**
     * 提额后可借款金额
     * <p>通过营销的方案提额等级调用额度试算接口试算返回</p>
     */
    private BigDecimal vipCanWithdralAmount;

    /**
     * 分流方id
     */
    private Integer shuntSupplierId;

    /**
     * 是否符合折扣 默认不符合 = 0  符合：1
     */
    private Integer showDiscountInfo = 0;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 折扣结束营销时间和当前时间的相差ms数
     */
    private Long discountEndTime;

    /**
     * 折扣价格
     */
    private BigDecimal discountPrice;

    /**
     * 展示文案内容, 0=不展示；1=开通文案；2=提额文案；3=息费折扣文案; 4=风控接口异常 5=新人礼包-提额文案 6=新人礼包-加速文案 7=方案无提额项
     */
    private Integer showContent;

    /**
     * 风控可提额度
     * <p>风控返回</p>
     */
    private BigDecimal raiseAmount;

    /**
     * 1=确认借款提额卡样式-动效 样式1 右上角展示 2=确认借款提额卡样式-按钮开关 样式2 确认借款页中部展示 3=确认借款提额卡样式-无动效 样式3 右上角展示
     * <p>默认为样式3，桔策返回</p>
     */
    private Integer quotaStyle = 3;

    /**
     * 提额弹窗-确认借款页，1=弹窗 0=不弹窗。默认不弹窗
     * <p>桔策返回</p>
     */
    private Integer quotaPopup = 0;

    /**
     * 7天内购买提额会员标识 0 否，1是
     */
    private Integer buyQuotaFlag = 0;

    /**
     * 是否支持后付款 1支持 2不支持
     */
    private Integer afterPayState;

    /**
     * 提额卡广告：1：展示，2：不展示（默认）
     * <p>桔策返回</p>
     */
    private Integer advertisement = 2;

    /**
     * 7天内买过的提额卡会员-后付款，待支付订单（有条件取消可以取消的）
     */
    private String afterPayOrder;

    /**
     * 有条件取消可以取消的订单对应的方案id
     */
    private Integer afterPayProgramId;

    /**
     * 小额月卡结清返现金额
     */
    private BigDecimal settleCashbackAmount;

    /**
     * 延时划扣时间，单位：分钟
     */
    private Integer delayTime;

    /**
     * 分流主体名称
     */
    private String supplierName;

    /**
     * 分流主体合同列表
     */
    private List<PlusShuntSupplierContractEntity> contractList;

    /**
     * 营销样式
     */
    private String styleCode;

    /**
     * 会员权益
     */
    private List<PlusModelEntity> plusRightsList;


    private String noMarketReason;

    /**
     * 支付类型
     */
    private List<Integer> payTypes;

    /**
     * 首付支付金额
     */
    private BigDecimal firstPayAmount;
}
