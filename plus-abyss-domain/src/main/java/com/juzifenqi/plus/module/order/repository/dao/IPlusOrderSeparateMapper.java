package com.juzifenqi.plus.module.order.repository.dao;

import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemEntity;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparatePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会员订单分账记录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/3 15:46
 */
@Mapper
public interface IPlusOrderSeparateMapper {

    /**
     * 新增返回ID
     */
    Integer savePlusOrderSeparate(PlusOrderSeparatePo po);

    /**
     * 更新
     */
    Integer updatePlusOrderSeparate(@Param("plusOrderSeparate") PlusOrderSeparatePo po);


    PlusOrderSeparatePo getById(Long id);

    /**
     * 通过订单号+申请流水号查询分流信息
     */
    PlusOrderSeparatePo getOrderSeparateByApplyNo(@Param("orderSn") String orderSn,
            @Param("applySerialNo") String applySerialNo);

    /**
     * 通过订单号查询
     */
    List<PlusOrderSeparatePo> getOrderSeparateByOrderNo(@Param("orderSn") String orderSn);

    /**
     * 通过订单号+分账状态查询分流信息
     */
    PlusOrderSeparatePo getOrderSeparateByStatus(@Param("orderSn") String orderSn,
            @Param("separateState") Integer separateState);

    /**
     * 通过订单号获取最近一条分账记录
     */
    PlusOrderSeparatePo getLastByOrderSn(String orderSn);

    /**
     * 使用订单号、订单支付行为为条件，更新清分记录状态
     * @param orderSn 订单号
     * @param orderPayAction 订单支付行为 1 主动支付，2 划扣支付
     * @param separateState 分账状态 1_分账处理中 2_分账成功 3_分账失败
     * @return 数据更新成功的条数
     */
    Integer updatePlusOrderSeparateState(@Param("orderSn") String orderSn,
            @Param("orderPayAction") int orderPayAction, @Param("separateState") int separateState);
}
