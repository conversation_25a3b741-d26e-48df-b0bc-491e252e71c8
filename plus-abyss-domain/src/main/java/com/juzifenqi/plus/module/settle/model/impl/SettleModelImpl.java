package com.juzifenqi.plus.module.settle.model.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.core.date.DateUtils;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.constants.PaySourceConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.constants.SerialNoPrefixConstant;
import com.juzifenqi.plus.dto.req.admin.settle.SettleBillQueryReq;
import com.juzifenqi.plus.dto.req.admin.settle.SettlePendingQueryReq;
import com.juzifenqi.plus.enums.ChannelEnum;
import com.juzifenqi.plus.enums.RefundStateEnum;
import com.juzifenqi.plus.enums.SeparateStateEnum;
import com.juzifenqi.plus.enums.pay.BankAccountTypeEnum;
import com.juzifenqi.plus.enums.pay.PayStateCodeEnum;
import com.juzifenqi.plus.enums.settle.IsCreateSettleEnum;
import com.juzifenqi.plus.enums.settle.SettleStateEnum;
import com.juzifenqi.plus.enums.supplier.SeparateTypeEnum;
import com.juzifenqi.plus.enums.supplier.SupplierSeparateEnableStateEnum;
import com.juzifenqi.plus.enums.supplier.SupplierSettleEnableStateEnum;
import com.juzifenqi.plus.enums.supplier.SupplierTypeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.common.IFmsRepository;
import com.juzifenqi.plus.module.common.IIMRepository;
import com.juzifenqi.plus.module.common.IPlusShuntRepository;
import com.juzifenqi.plus.module.common.converter.condtions.ShuntCondition;
import com.juzifenqi.plus.module.common.entity.DefrayPayResultEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierPayEntity;
import com.juzifenqi.plus.module.common.event.DefrayApplyEvent;
import com.juzifenqi.plus.module.order.model.IPlusOrderRefundInfoModel;
import com.juzifenqi.plus.module.order.model.PlusOrderQueryModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderSeparateRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderShuntRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.pay.NewDefrayResultCallbackEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemEntity;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparateItemPo;
import com.juzifenqi.plus.module.settle.model.SettleModel;
import com.juzifenqi.plus.module.settle.model.contract.PlusOrderSettleItemPendingRepository;
import com.juzifenqi.plus.module.settle.model.contract.SettleBillApplyRepository;
import com.juzifenqi.plus.module.settle.model.contract.SettleBillRepository;
import com.juzifenqi.plus.module.settle.model.contract.entity.PlusOrderSettleItemPendingAdminEntity;
import com.juzifenqi.plus.module.settle.model.contract.entity.PlusOrderSettleItemPendingEntity;
import com.juzifenqi.plus.module.settle.model.contract.entity.SettleBillApplyEntity;
import com.juzifenqi.plus.module.settle.model.contract.entity.SettleBillEntity;
import com.juzifenqi.plus.module.settle.model.converter.ISettleModelConverter;
import com.juzifenqi.plus.module.settle.model.event.CreateSettleBillEvent;
import com.juzifenqi.plus.module.settle.model.event.UpdateDefrayResultEvent;
import com.juzifenqi.plus.utils.RedisUtils;
import com.juzifenqi.plus.utils.SerialNoUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.juzishuke.dss.common.enums.DataTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 结算
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/3 13:59
 */
@Component
@Slf4j
public class SettleModelImpl implements SettleModel {

    private final ISettleModelConverter converter = ISettleModelConverter.instance;

    @Autowired
    private IIMRepository                        imRepository;
    @Autowired
    private IPlusOrderSeparateRepository         separateRepository;
    @Autowired
    private PlusOrderQueryModel                  plusOrderQueryModel;
    @Autowired
    private IPlusOrderRefundInfoModel plusOrderRefundInfoModel;
    @Autowired
    private PlusOrderSettleItemPendingRepository pendingRepository;
    @Autowired
    private SettleBillRepository                 settleBillRepository;
    @Autowired
    private IPlusShuntRepository                 shuntRepository;
    @Autowired
    private SettleBillApplyRepository            applyRepository;
    @Autowired
    private IFmsRepository                       fmsRepository;
    @Autowired
    private RedisUtils                           redisUtils;
    @Autowired
    private ShuntCondition                       shuntCondition;
    @Autowired
    private IPlusOrderShuntRepository plusOrderShuntRepository;

    /**
     * 会员订单退款通知处理待结算
     */
    @Override
    public Boolean refundSettlePending(OrderRefundNotifyEntity entity) {
        try {
            log.info("会员订单退款通知处理待结算开始 {}", JSONObject.toJSONString(entity));
            if (entity == null || StringUtils.isBlank(entity.getOrderId()) || StringUtils.isBlank(
                    entity.getStatus()) || entity.getRefundAmount() == null) {
                throw new PlusAbyssException(
                        "会员订单退款通知处理待结算，订单回调数据异常 " + JSONObject.toJSONString(
                                entity));
            }
            String orderSn = entity.getOrderId();
            if (!RefundStateEnum.S.getCode().equals(entity.getStatus())) {
                log.info("会员订单退款通知处理待结算，非退款成功不处理待结算 {}", orderSn);
                return true;
            }
            //查询退款明细中是否有数据
            PlusOrderRefundDetailEntity detailEntity =  plusOrderRefundInfoModel.getDetailByRefundSerialNo(entity.getThirdPayNum());
            if (detailEntity != null) {
                return firstPayOrderRefundSettlePending(entity, detailEntity);
            }
            // 查询pending记录，不能重复生成待结算数据
            List<PlusOrderSettleItemPendingEntity> entities = pendingRepository.getSettlePendingItemsByOrderSn(
                    entity.getOrderId());
            if (!CollectionUtils.isEmpty(entities)) {
                log.info("会员订单退款通知处理待结算，该订单已处理过，不重复处理 {}", orderSn);
                return true;
            }
            // 查询订单的分账记录
            PlusOrderSeparateEntity separateEntity = separateRepository.getOrderSeparateByStatus(
                    orderSn, SeparateStateEnum.SUCCESS.getCode());
            if (separateEntity == null) {
                log.info("会员订单退款通知处理待结算，无分账成功记录不处理待结算 {}", orderSn);
                return true;
            }
            // 只有清分了并且需要结算的数据才需要处理待结算
            if (!SupplierSeparateEnableStateEnum.YES.getCode()
                    .equals(separateEntity.getSeparateEnableState())
                    || !SupplierSettleEnableStateEnum.YES.getCode()
                    .equals(separateEntity.getSettleEnableState())) {
                log.info("会员订单退款通知处理待结算，订单不需要清分或者结算，退款不处理待结算 {}",
                        orderSn);
                return true;
            }
            // 查询订单信息
            PlusOrderEntity plusOrderInfo = plusOrderQueryModel.getByOrderSn(orderSn);
            if (plusOrderInfo == null) {
                throw new PlusAbyssException(
                        "会员订单退款通知处理待结算，未查询到会员订单 " + orderSn);
            }
            // 查询分账明细
            PlusOrderSeparateItemPo query = new PlusOrderSeparateItemPo();
            query.setOrderSn(orderSn);
            query.setApplySerialNo(separateEntity.getApplySerialNo());
            query.setSupplierType(SupplierTypeEnum.QF.getCode());
            List<PlusOrderSeparateItemEntity> itemEntities = separateRepository.getPlusOrderSeparateItems(
                    query);
            if (CollectionUtils.isEmpty(itemEntities)) {
                throw new PlusAbyssException(
                        "会员订单退款通知处理待结算，未查询到分账明细 " + orderSn);
            }
            // 计算待结算金额
            List<PlusOrderSettleItemPendingEntity> pendingEntities = calSettleItemPending(entity,
                    plusOrderInfo, separateEntity, itemEntities);
            pendingRepository.saveOrderSettleItemPending(pendingEntities);
            log.info("会员订单退款通知处理待结算结束 {}", orderSn);
            return true;
        } catch (Exception e) {
            LogUtil.printLog(e, "会员订单退款通知处理待结算异常");
            imRepository.sendImMessage(e.getMessage());
            // 因业务异常导致取消失败,无需重试
            return e instanceof PlusAbyssException;
        }
    }

    private boolean firstPayOrderRefundSettlePending(OrderRefundNotifyEntity entity,PlusOrderRefundDetailEntity refundDetailEntity) {
        String orderSn = entity.getOrderId();
        // 查询pending记录，不能重复生成待结算数据
        PlusOrderSeparateEntity separateEntity = separateRepository.getById(refundDetailEntity.getSeparateId());
        if (separateEntity == null || !Objects.equals(separateEntity.getSeparateState(), SeparateStateEnum.SUCCESS.getCode())) {
            log.info("会员订单退款通知处理待结算，无分账成功记录不处理待结算 {}", orderSn);
            return true;
        }
        List<PlusOrderSettleItemPendingEntity> entities = pendingRepository.getByOrderSnAndSerialNo(
                orderSn, separateEntity.getApplySerialNo());
        if (!CollectionUtils.isEmpty(entities)) {
            log.info("会员订单退款通知处理待结算，该订单已处理过，不重复处理 {}", orderSn);
            return true;
        }
        // 只有清分了并且需要结算的数据才需要处理待结算
        if (!SupplierSeparateEnableStateEnum.YES.getCode()
                .equals(separateEntity.getSeparateEnableState())
                || !SupplierSettleEnableStateEnum.YES.getCode()
                .equals(separateEntity.getSettleEnableState())) {
            log.info("会员订单退款通知处理待结算，订单不需要清分或者结算，退款不处理待结算 {}",
                    orderSn);
            return true;
        }
        // 查询分账明细
        PlusOrderSeparateItemPo query = new PlusOrderSeparateItemPo();
        query.setOrderSn(orderSn);
        query.setApplySerialNo(separateEntity.getApplySerialNo());
        query.setSupplierType(SupplierTypeEnum.QF.getCode());
        List<PlusOrderSeparateItemEntity> itemEntities = separateRepository.getPlusOrderSeparateItems(
                query);
        if (CollectionUtils.isEmpty(itemEntities)) {
            throw new PlusAbyssException(
                    "会员订单退款通知处理待结算，未查询到分账明细 " + orderSn);
        }
        // 查询订单信息
        PlusOrderEntity plusOrderInfo = plusOrderQueryModel.getByOrderSn(orderSn);
        if (plusOrderInfo == null) {
            throw new PlusAbyssException(
                    "会员订单退款通知处理待结算，未查询到会员订单 " + orderSn);
        }
        // 计算待结算金额
        List<PlusOrderSettleItemPendingEntity> pendingEntities = calSettleItemPending(entity,
                plusOrderInfo, separateEntity, itemEntities);
        pendingRepository.saveOrderSettleItemPending(pendingEntities);
        log.info("会员订单退款通知处理待结算结束 {}", orderSn);
        return true;
    }

    /**
     * 计算待结算金额
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/3 16:30
     */
    private List<PlusOrderSettleItemPendingEntity> calSettleItemPending(
            OrderRefundNotifyEntity entity, PlusOrderEntity plusOrderInfo,
            PlusOrderSeparateEntity separateEntity,
            List<PlusOrderSeparateItemEntity> itemEntities) {
        List<PlusOrderSettleItemPendingEntity> pendingEntities = new ArrayList<>();
        itemEntities.forEach(itemEntity -> {
            PlusOrderSettleItemPendingEntity pendingEntity = new PlusOrderSettleItemPendingEntity();
            pendingEntity.setChannelId(plusOrderInfo.getChannelId());
            pendingEntity.setOrderSn(entity.getOrderId());
            pendingEntity.setOriApplySerialNo(separateEntity.getApplySerialNo());
            // refund_success_time退款成功时间以会员收到mq消息的时间为准，不以订单报文内的退款成功时间为依据
            pendingEntity.setRefundSuccessTime(new Date());
            pendingEntity.setShuntSupplierId(separateEntity.getShuntSupplierId());
            pendingEntity.setSeparateSupplierId(itemEntity.getSupplierId());
            pendingEntity.setSeparateType(itemEntity.getSeparateType());
            pendingEntity.setRefundAmount(entity.getRefundAmount());
            pendingEntity.setIsCreateSettle(IsCreateSettleEnum.NOT_CREATE.getCode());
            BigDecimal amount;
            if (plusOrderInfo.getOrderAmount().equals(entity.getRefundAmount())) {
                // 全款退货
                amount = itemEntity.getSeparateAmount();
            } else {
                // 部分退款
                BigDecimal rate = itemEntity.getSeparateRate();
                if (SeparateTypeEnum.FIXED_AMOUNT.getCode().equals(itemEntity.getSeparateType())) {
                    // 固定金额 计算比例=清分金额/订单金额，保留四位小数，四舍五入
                    rate = itemEntity.getSeparateAmount()
                            .divide(separateEntity.getTotalSeparateAmount(), 4,
                                    RoundingMode.HALF_UP);
                }
                // 结算金额=退款金额*比例，保留两位小数，四舍五入
                amount = entity.getRefundAmount().multiply(rate).setScale(2, RoundingMode.HALF_UP);
                // 金额校验，防止退超
                if (amount.compareTo(itemEntity.getSeparateAmount()) > 0) {
                    throw new PlusAbyssException(
                            "会员订单退款通知处理待结算，计算结算金额异常 " + entity.getOrderId()
                                    + " 分流主体:" + separateEntity.getShuntSupplierId() + " 清分主体:"
                                    + itemEntity.getSupplierId());
                }
            }
            pendingEntity.setSettleAmount(amount);
            pendingEntities.add(pendingEntity);
        });
        return pendingEntities;
    }

    /**
     * 跑批生成结算单
     */
    @Override
    public void createSettleBillJob(String beginTime, String endTime) {
        try {
            log.info("跑批生成结算单，入参:{} {}", beginTime, endTime);
            if (StringUtils.isEmpty(beginTime) || StringUtils.isEmpty(endTime)) {
                throw new PlusAbyssException("跑批生成结算单，时间范围不允许为空");
            }
            // 查询待结算主体信息
            List<PlusOrderSettleItemPendingEntity> pendingSupplierEntities = pendingRepository.getSettlePendingSupplierByDate(
                    beginTime, endTime, IsCreateSettleEnum.NOT_CREATE.getCode());
            log.info("跑批生成结算单，查询待结算主体信息返回:{}",
                    JSON.toJSONString(pendingSupplierEntities));
            if (CollectionUtils.isEmpty(pendingSupplierEntities)) {
                return;
            }
            // 生成结算单
            pendingSupplierEntities.forEach(entity -> {
                try {
                    createSettleBill(beginTime, endTime, entity);
                } catch (Exception e) {
                    LogUtil.printLog(e, "生成单笔结算单异常 " + entity.getShuntSupplierId() + ","
                            + entity.getSeparateSupplierId());
                    imRepository.sendImMessage(e.getMessage());
                }
            });
            log.info("跑批生成结算单，结束:{} {}", beginTime, endTime);
        } catch (Exception e) {
            LogUtil.printLog(e, "跑批生成结算单异常");
            imRepository.sendImMessage(e.getMessage());
        }
    }

    /**
     * 生成结算单
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/9 18:22
     */
    private void createSettleBill(String beginTime, String endTime,
            PlusOrderSettleItemPendingEntity pendingSupplierEntity) {
        log.info("生成结算单，入参：{}", JSON.toJSONString(pendingSupplierEntity));
        // 查询待结算明细
        List<PlusOrderSettleItemPendingEntity> pendingEntities = pendingRepository.getSettlePendingByDate(
                beginTime, endTime, pendingSupplierEntity.getShuntSupplierId(),
                pendingSupplierEntity.getSeparateSupplierId(),
                IsCreateSettleEnum.NOT_CREATE.getCode(),
                pendingSupplierEntity.getChannelId());
        log.info("生成结算单，查询待结算明细返回:{}", JSON.toJSONString(pendingEntities));
        if (CollectionUtils.isEmpty(pendingEntities)) {
            throw new PlusAbyssException("生成结算单，未查询到待结算明细");
        }
        // 生成保存结算单
        settleBillRepository.saveSettleBill(getCreateSettleBillEvent(pendingEntities));
    }

    /**
     * 获取生成结算单Event
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/5 13:50
     */
    private CreateSettleBillEvent getCreateSettleBillEvent(
            List<PlusOrderSettleItemPendingEntity> pendingEntities) {
        CreateSettleBillEvent event = new CreateSettleBillEvent();
        // 待结算明细ids
        event.setPendingIds(pendingEntities.stream().map(PlusOrderSettleItemPendingEntity::getId)
                .collect(Collectors.toList()));
        // 结算单
        SettleBillEntity settleBillEntity = new SettleBillEntity();
        event.setSettleBillEntity(settleBillEntity);
        // 待结算日期=当天
        settleBillEntity.setSettlePendingDate(
                DateUtils.getDateFromStr(DateUtils.getSysCurFmtDate()));
        // 结算单号：yyMMddHHmmssSSS+五位随机数
        settleBillEntity.setSettleBillNo(SerialNoUtils.generateSettleBillNo());
        settleBillEntity.setShuntSupplierId(pendingEntities.get(0).getShuntSupplierId());
        settleBillEntity.setSeparateSupplierId(pendingEntities.get(0).getSeparateSupplierId());
        settleBillEntity.setChannelId(pendingEntities.get(0).getChannelId());
        settleBillEntity.setSettleAmount(
                pendingEntities.stream().map(PlusOrderSettleItemPendingEntity::getSettleAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
        settleBillEntity.setSettleState(SettleStateEnum.PENDING.getCode());
        return event;
    }

    /**
     * 结算单跑批发起代付
     */
    @Override
    public void defraySettleBillJob(String date) {
        log.info("结算单跑批发起代付开始，入参:{}", date);
        // 查询代付任务
        List<SettleBillEntity> settleBillEntities = settleBillRepository.getWaitDefraySettleBillEntities(
                date, SettleStateEnum.PENDING.getCode());
        log.info("结算单跑批发起代付，查询待发起结算单返回:{}",
                JSON.toJSONString(settleBillEntities));
        if (CollectionUtils.isEmpty(settleBillEntities)) {
            return;
        }
        // 批量修改为处理中
        settleBillRepository.batchProcessing(
                settleBillEntities.stream().map(SettleBillEntity::getId)
                        .collect(Collectors.toList()), SettleStateEnum.PROCESSING.getCode());
        // 发起代付
        settleBillEntities.forEach(this::doSettleBillDefrayPay);
        log.info("结算单跑批发起代付开始，结束:{}", date);
    }

    /**
     * 结算单发起代付
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/5 20:10
     */
    private void doSettleBillDefrayPay(SettleBillEntity settleBillEntity) {
        Long billId = settleBillEntity.getId();
        log.info("结算单发起代付，入参：{}", JSON.toJSONString(settleBillEntity));
        try {
            // 查询清分主体缓存
            PlusShuntSupplierEntity separateSupplierCache = shuntRepository.getSupplierCache(
                    settleBillEntity.getSeparateSupplierId());
            if (separateSupplierCache == null) {
                throw new PlusAbyssException(
                        "结算代付查询清分主体缓存数据异常 " + settleBillEntity.getSeparateSupplierId());
            }
            // 获取结算单代付申请记录
            SettleBillApplyEntity applyEntity = getSettleBillApplyEntity(settleBillEntity, separateSupplierCache);
            // 保存结算单代付申请和操作日志
            Long id = applyRepository.saveSettleBillApply(applyEntity, "system", "system");
            applyEntity.setId(id);
            // 发起结算代付
            Integer channelId = settleBillEntity.getChannelId();
            channelId = channelId == null ? ChannelEnum.A.getCode() : channelId;
            String tradeSubject = "YKD" + separateSupplierCache.getSupplierName() + "主体清分";
            DefrayApplyEvent event = converter.toDefrayApplyEvent(settleBillEntity, applyEntity,
                    PaySourceConstant.PAY_SOURCE_SETTLE, tradeSubject, channelId);
            DefrayPayResultEntity result = fmsRepository.cDefrayPay(event);
            // 处理代付申请结果
            dealDefrayResult(result, applyEntity);
        } catch (Exception e) {
            LogUtil.printLog(e, "结算单发起代付异常 " + billId);
            imRepository.sendImMessage(e.getMessage());
        }
    }

    /**
     * 获取结算单代付申请记录
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/5 19:46
     */
    private SettleBillApplyEntity getSettleBillApplyEntity(SettleBillEntity settleBillEntity,
                                                           PlusShuntSupplierEntity separateSupplierCache) {
        // 查询清分主体缓存
//        PlusShuntSupplierEntity separateSupplierCache = shuntRepository.getSupplierCache(
//                settleBillEntity.getSeparateSupplierId());
//        if (separateSupplierCache == null || separateSupplierCache.getPay() == null
//                || StringUtils.isBlank(separateSupplierCache.getPay().getBusinessScene())) {
//            throw new PlusAbyssException(
//                    "結算代付查询清分主体缓存数据异常 " + settleBillEntity.getSeparateSupplierId());
//        }
        // 查询分流主体缓存
        PlusShuntSupplierEntity shuntSupplierCache = getSettleBillDefrayShuntSupplier(
                settleBillEntity.getShuntSupplierId());
        // 结算单申请记录
        SettleBillApplyEntity applyEntity = new SettleBillApplyEntity();
        applyEntity.setSettleBillId(settleBillEntity.getId());
        applyEntity.setApplySerialNo(
                SerialNoUtils.generateApplySerialNo(SerialNoPrefixConstant.SERIAL_NO_PREFIX_JSDF,
                        settleBillEntity.getSettleBillNo()));
        // 业务场景：清分主体
        applyEntity.setBusinessScene(separateSupplierCache.getPay().getBusinessScene());
        // 收款方：分流主体
        PlusShuntSupplierPayEntity pay = shuntSupplierCache.getPay();
        applyEntity.setBankAccountNo(pay.getBankAccountNo());
        applyEntity.setBankAccountName(pay.getBankAccountName());
        applyEntity.setBankAccountType(pay.getBankAccountType());
        applyEntity.setBankBranchNo(pay.getBankBranchNo());
        applyEntity.setBankName(pay.getBankName());
        applyEntity.setBankCityName(pay.getBankCityName());
        applyEntity.setBankProvinceName(pay.getBankProvinceName());
        applyEntity.setSettleState(SettleStateEnum.PROCESSING.getCode());
        return applyEntity;
    }

    /**
     * 校验结算代付分流主体配置
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/5 20:31
     */
    private PlusShuntSupplierEntity getSettleBillDefrayShuntSupplier(Integer supplierId) {
        PlusShuntSupplierEntity shuntSupplierCache = shuntRepository.getSupplierCache(supplierId);
        if (shuntSupplierCache == null || StringUtils.isBlank(shuntSupplierCache.getSupplierName())
                || shuntSupplierCache.getPay() == null || StringUtils.isBlank(
                shuntSupplierCache.getPay().getBankAccountNo()) || StringUtils.isBlank(
                shuntSupplierCache.getPay().getBankAccountName())
                || shuntSupplierCache.getPay().getBankAccountType() == null) {
            throw new PlusAbyssException("結算代付查询分流主体缓存数据异常 " + supplierId);
        }
        // 对公校验
        if (BankAccountTypeEnum.TO_PUBLIC.getType()
                .equals(shuntSupplierCache.getPay().getBankAccountType())) {
            if (StringUtils.isBlank(shuntSupplierCache.getPay().getBankBranchNo())
                    || StringUtils.isBlank(shuntSupplierCache.getPay().getBankName())
                    || StringUtils.isBlank(shuntSupplierCache.getPay().getBankCityName())
                    || StringUtils.isBlank(shuntSupplierCache.getPay().getBankProvinceName())) {
                throw new PlusAbyssException(
                        "結算代付查询分流主体缓存对公配置异常 " + shuntSupplierCache.getId());
            }
        }
        return shuntSupplierCache;
    }

    /**
     * 处理代付申请结果
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/6 09:45
     */
    private void dealDefrayResult(DefrayPayResultEntity result, SettleBillApplyEntity applyEntity) {
        // 接口返回超时或者异常，飞书预警
        if (result == null) {
            throw new PlusAbyssException("结算单发起代付申请调用新支付系统异常");
        }
        SettleBillApplyEntity billApplyEntity = new SettleBillApplyEntity();
        billApplyEntity.setId(applyEntity.getId());
        billApplyEntity.setPaySerialNumber(result.getSerialNumber());
        if (!PayStateCodeEnum.I.getCode().equals(result.getTradeStatus())) {
            billApplyEntity.setSettleState(SettleStateEnum.FAIL.getCode());
            billApplyEntity.setRemark(result.getFailReason());
        }
        // 更新代付结果
        updateDefrayResult(billApplyEntity, applyEntity.getSettleBillId(),
                !PayStateCodeEnum.I.getCode().equals(result.getTradeStatus()));
    }

    /**
     * 结算单代付结果回调
     */
    @Override
    public void defrayResultCallback(NewDefrayResultCallbackEntity entity) {
        log.info("结算单代付结果回调处理开始 {}", JSONObject.toJSONString(entity));
        if (entity == null || StringUtils.isBlank(entity.getThirdPayNum()) || StringUtils.isBlank(
                entity.getDefrayStatus())) {
            throw new PlusAbyssException(
                    "结算单代付结果回调处理，支付回调数据异常 " + JSONObject.toJSONString(entity));
        }
        if (!PayStateCodeEnum.S.getCode().equals(entity.getDefrayStatus())
                && !PayStateCodeEnum.F.getCode().equals(entity.getDefrayStatus())) {
            throw new PlusAbyssException(
                    "结算单代付结果回调处理，未知的回调状态 " + entity.getDefrayStatus() + ","
                            + entity.getDefrayStatus());
        }
        String applySerialNo = entity.getThirdPayNum();
        // 查询结算单代付申请记录
        SettleBillApplyEntity applyEntity = applyRepository.getSettleBillApplyByApplySerialNo(
                applySerialNo);
        if (applyEntity == null) {
            throw new PlusAbyssException(
                    "结算单代付结果回调处理，业务请求流水号无记录 " + applySerialNo);
        }
        // 终态不处理
        if (SettleStateEnum.SUCCESS.getCode().equals(applyEntity.getSettleState())
                || SettleStateEnum.FAIL.getCode().equals(applyEntity.getSettleState())) {
            log.info("结算单代付结果回调处理，结算申请记录已是终态无法处理 {} {}",
                    applyEntity.getSettleBillId(), applySerialNo);
            return;
        }
        // 查询结算单信息
        SettleBillEntity settleBillEntity = settleBillRepository.getSettleBillById(
                applyEntity.getSettleBillId());
        if (settleBillEntity == null) {
            throw new PlusAbyssException(
                    "结算单代付结果回调处理，查询不到结算单 " + applyEntity.getSettleBillId());
        }
        if (SettleStateEnum.SUCCESS.getCode().equals(settleBillEntity.getSettleState())) {
            log.info("结算单代付结果回调处理，结算单已是成功状态无法处理 {}",
                    settleBillEntity.getId());
            return;
        }
        SettleBillApplyEntity billApplyEntity = new SettleBillApplyEntity();
        billApplyEntity.setId(applyEntity.getId());
        billApplyEntity.setPayCallbackTime(new Date());
        billApplyEntity.setSettleState(PayStateCodeEnum.S.getCode().equals(entity.getDefrayStatus())
                ? SettleStateEnum.SUCCESS.getCode() : SettleStateEnum.FAIL.getCode());
        if (!PayStateCodeEnum.S.getCode().equals(entity.getDefrayStatus())) {
            if (StringUtils.isNotBlank(entity.getErrorMsg())
                    && entity.getErrorMsg().length() > 150) {
                billApplyEntity.setRemark(entity.getErrorMsg().substring(150));
            } else {
                billApplyEntity.setRemark(entity.getErrorMsg());
            }
        }
        // 更新代付结果
        updateDefrayResult(billApplyEntity, applyEntity.getSettleBillId(), true);
        log.info("结算单代付结果回调处理结束 {}", applySerialNo);
    }

    /**
     * 更新代付结果
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/6 14:16
     */
    private void updateDefrayResult(SettleBillApplyEntity billApplyEntity, Long settleBillId,
            Boolean dealBill) {
        UpdateDefrayResultEvent event = new UpdateDefrayResultEvent();
        event.setApplyEntity(billApplyEntity);
        if (dealBill) {
            // 结算单
            SettleBillEntity billEntity = new SettleBillEntity();
            event.setBillEntity(billEntity);
            billEntity.setId(settleBillId);
            billEntity.setSettleState(billApplyEntity.getSettleState());
            billEntity.setPayCallbackTime(billApplyEntity.getPayCallbackTime());
            billEntity.setRemark(billApplyEntity.getRemark());
        }
        // 更新同步代付结果
        applyRepository.updateDefrayResult(event);
    }

    /**
     * 分页查询待结算列表
     */
    @Override
    public List<PlusOrderSettleItemPendingAdminEntity> getPendingPageList(
            SettlePendingQueryReq req) {
        return pendingRepository.getPendingPageList(req);
    }

    /**
     * 查询待结算总条数
     */
    @Override
    public Integer pendingPageListCount(SettlePendingQueryReq req) {
        return pendingRepository.pendingPageListCount(req);
    }

    /**
     * 分页查询结算单列表
     */
    @Override
    public List<SettleBillEntity> getSettleBillPageList(SettleBillQueryReq req) {
        return settleBillRepository.getSettleBillPageList(req);
    }

    /**
     * 查询结算单总条数
     */
    @Override
    public Integer settleBillPageListCount(SettleBillQueryReq req) {
        return settleBillRepository.settleBillPageListCount(req);
    }

    @Override
    public void initSettleBankAccountNoUuid(Integer size) {
        long lastId = 0L;
        if (redisUtils.hasKey(RedisConstantPrefix.SETTLE_BILL_APPLY_BEGIN_ID)) {
            lastId = Long.parseLong(redisUtils.get(RedisConstantPrefix.SETTLE_BILL_APPLY_BEGIN_ID));
        }
        log.info("结算单申请表跑批发起新银行卡密文初始化开始入参,last:{},size:{}", lastId, size);
        List<SettleBillApplyEntity> settleBillApplyEntities = applyRepository.getWithEmptyBankAccountNoUuid(lastId, size);
        if (CollectionUtils.isEmpty(settleBillApplyEntities)) {
            log.info("结算单申请表跑批发起新银行卡密文初始化，无待初始化结算单申请表");
            return;
        }
        List<String> content = settleBillApplyEntities.stream().map(SettleBillApplyEntity::getBankAccountNo).collect(Collectors.toList());
        Map<String, String> toUuidMap = shuntCondition.desToUuidBatch(content, DataTypeEnum.BANK_CARD);
        if (toUuidMap.isEmpty()){
            return;
        }
        for (SettleBillApplyEntity settleBillApplyEntity : settleBillApplyEntities) {
            if (toUuidMap.containsKey(settleBillApplyEntity.getBankAccountNo())) {
                settleBillApplyEntity.setBankAccountNoUuid(toUuidMap.get(settleBillApplyEntity.getBankAccountNo()));
            }
        }
        applyRepository.updateBatchBankAccountNoUuid(settleBillApplyEntities);
        // 更新Redis中的lastId为最后一条记录的ID
        SettleBillApplyEntity lastRecord = settleBillApplyEntities.get(settleBillApplyEntities.size() - 1);
        redisUtils.setEx(RedisConstantPrefix.SETTLE_BILL_APPLY_BEGIN_ID, String.valueOf(lastRecord.getId()),1, TimeUnit.HOURS);
        log.info("结算单申请表跑批发起新银行卡密文初始化，结束:{}", size);
    }

    @Override
    public void inspectSettleBankAccountNoUuid(Integer size) {
        long lastId = 0L;
        log.info("结算单申请表跑批新银行卡密文巡检开始入参,last:{},size:{}", lastId, size);
        List<SettleBillApplyEntity> settleBillApplyEntities = applyRepository.getWithBankAccountNoUuid(lastId, size);
        if (CollectionUtils.isEmpty(settleBillApplyEntities)) {
            log.info("结算单申请表跑批新银行卡密文巡检，无待初始化结算单申请表");
            return;
        }
        List<String> content = settleBillApplyEntities.stream().map(SettleBillApplyEntity::getBankAccountNo).collect(Collectors.toList());
        Map<String, String> toUuidMap = shuntCondition.desToUuidBatch(content, DataTypeEnum.BANK_CARD);
        if (toUuidMap.isEmpty()){
            return;
        }
        for (SettleBillApplyEntity settleBillApplyEntity : settleBillApplyEntities) {
            String bankAccountNo = settleBillApplyEntity.getBankAccountNo();
            String bankAccountNoUuid = settleBillApplyEntity.getBankAccountNoUuid();
            if (toUuidMap.containsKey(bankAccountNo)) {
                String m = toUuidMap.get(bankAccountNo);
                if (!bankAccountNoUuid.equals(m)) {
                    log.info("结算单申请表跑批新银行卡密文巡检校验错误,id:{},bankAccountNo:{},bankAccountNoUuid:{},m:{}",
                            settleBillApplyEntity.getId(), bankAccountNo, bankAccountNoUuid, m);
                }
            }
        }
        log.info("结算单申请表跑批新银行卡密文巡检，结束:{}", size);
    }
}
