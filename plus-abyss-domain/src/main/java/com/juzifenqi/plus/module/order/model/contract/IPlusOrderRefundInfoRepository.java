package com.juzifenqi.plus.module.order.model.contract;

import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import java.util.List;

/**
 * 会员订单退款信息
 *
 * <AUTHOR>
 * @date 2024/9/2 11:01
 */
public interface IPlusOrderRefundInfoRepository {

    /**
     * 根据订单号、状态查询
     */
    List<PlusOrderRefundInfoEntity> getByOrderSn(String orderSn, List<Integer> refundStateList);

    /**
     * 保存信息
     */
    Long save(PlusOrderRefundInfoEntity entity);

    /**
     * 保存退款信息和明细
     */
    Long saveInfoAndDetail(PlusOrderRefundInfoEntity entity, List<PlusOrderRefundDetailEntity> details);

    /**
     * 保存退款明细
     * @param entity
     */
    void saveDetail(PlusOrderRefundDetailEntity entity);
    /**
     * 根据id进行查询
     */
    PlusOrderRefundInfoEntity getById(Long id);
    /**
     * 根据退款业务流水号查询
     */
    PlusOrderRefundInfoEntity getByRefundSerialNo(String refundSerialNo);

    PlusOrderRefundDetailEntity getDetailByRefundSerialNo(String refundSerialNo);

    PlusOrderRefundDetailEntity getDetailById(Long refundDetailId);

    List<PlusOrderRefundDetailEntity> getDetailsByInfoId(Long refundInfoId);

    /**
     * 更新状态
     */
    void updateRefundState(String refundSerialNo, Integer refundState);

    /**
     * 更新退款记录和明细状态
     */
    void updateRefundInfoAndDetailState(Long refundInfoId, List<Long> detailIds, Integer refundState);


    void updateRefundDetailBySerialNo(String serialNo, String paySerialNo);

    void updateRefundDetail(PlusOrderRefundDetailEntity detailEntity);

    /**
     * 修改支付退款流水号
     */
    void updatePaySerialNo(String refundSerialNo, String paySerialNo);

    /**
     * 修改支付退款流水号
     */
    void updatePaySerialNo(Long id, String paySerialNo);

    /**
     * 根据id修改
     */
    void updateById(PlusOrderRefundInfoEntity entity);

    /**
     * 根据订单号获取最近一条退款信息
     */
    PlusOrderRefundInfoEntity getLastOneByOrderSn(String orderSn);
}
