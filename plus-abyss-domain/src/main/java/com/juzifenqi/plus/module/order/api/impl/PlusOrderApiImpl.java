package com.juzifenqi.plus.module.order.api.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.api.IPlusOrderApi;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.req.CancelRenewReq;
import com.juzifenqi.plus.dto.req.CreateOrderNoticeTaskReq;
import com.juzifenqi.plus.dto.req.PlusDeductForActiveReq;
import com.juzifenqi.plus.dto.req.PlusOrderCancelReq;
import com.juzifenqi.plus.dto.req.PlusOrderCreateReq;
import com.juzifenqi.plus.dto.req.PlusOrderRefundApplyReq;
import com.juzifenqi.plus.dto.req.PlusOrderRelationCreateReq;
import com.juzifenqi.plus.dto.req.PlusOrderRelationReq;
import com.juzifenqi.plus.dto.req.PlusPayCallbackReq;
import com.juzifenqi.plus.dto.req.PlusProductOrderCreateReq;
import com.juzifenqi.plus.dto.req.PlusRenewInfoReq;
import com.juzifenqi.plus.dto.req.UpdOrderStateReq;
import com.juzifenqi.plus.dto.req.VirtualGoodsOrderCreateReq;
import com.juzifenqi.plus.dto.req.VirtualOrderCreateReq;
import com.juzifenqi.plus.dto.req.profits.ProductCheckReq;
import com.juzifenqi.plus.dto.resp.*;
import com.juzifenqi.plus.dubbo.DubboService;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.VipErrorEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.contract.entity.ProductCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.VirtualCheckResultEntity;
import com.juzifenqi.plus.module.order.api.converter.IOrderApiConverter;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplyApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderNoticeApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderQueryApplication;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderCancelAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderDeductAo;
import com.juzifenqi.plus.module.order.application.ao.PlusPayCallbackAo;
import com.juzifenqi.plus.module.order.application.ao.PlusRenewInfoAo;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRefundApplyRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRefundInfoRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderRelationEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IMessageExternalRepository;
import com.juzifenqi.plus.module.order.model.event.CancelRenewEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;
import com.juzifenqi.plus.module.order.model.event.PlusRenewInfoEvent;
import com.juzifenqi.plus.module.order.model.event.notice.PlusOrderNoticeEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderMessageMqOutEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderPayCallbackEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRefundApplyEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRelationCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRelationEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusProductOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.UpdOrderStateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualGoodsOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualOrderCreateEvent;
import com.juzifenqi.plus.utils.RedisLock;
import java.math.BigDecimal;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class PlusOrderApiImpl implements IPlusOrderApi {

    @Autowired
    private IPlusOrderApplication       plusOrderApplication;
    @Autowired
    private IPlusOrderApplyApplication  orderRefundApplyApplication;
    @Autowired
    private IPlusOrderNoticeApplication noticeApplication;
    @Autowired
    private IPlusOrderQueryApplication  queryApplication;
    @Autowired
    private RedisLock                   redisLock;
    @Autowired
    private IMessageExternalRepository  messageExternalRepository;

    @Autowired
    private IPlusOrderRefundApplyRepository plusOrderRefundApplyRepository;

    @Autowired
    private IPlusOrderRefundInfoRepository plusOrderRefundInfoRepository;


    private final IOrderApiConverter orderApiConverter = IOrderApiConverter.instance;

    /**
     * 创建订单
     */
    @Override
    public PlusAbyssResult<PlusOrderCreateResp> createOrder(PlusOrderCreateReq plusOrderCreateReq) {
        PlusOrderCreateEvent plusOrderCreateEvent = orderApiConverter.toPlusOrderCreateEvent(
                plusOrderCreateReq);
        // 创建订单
        PlusOrderAo ao = plusOrderApplication.createPlusOrder(plusOrderCreateEvent);
        PlusOrderCreateResp plusOrderCreateResp = orderApiConverter.toPlusOrderCreateResp(ao);
        // 过度方案：开通会员成功，非权益卡和小额月卡，发mq给super-plus发放权益
        if (ao.isOpenCard()) {
            // 发送开通成功mq（这个非过度方案，切勿删除）
            PlusOrderMessageMqOutEvent orderMessageOutEvent = orderApiConverter.toPlusOrderMessageMqOutEvent(
                    1, ao, plusOrderCreateReq);
            messageExternalRepository.sendPlusMq(orderMessageOutEvent);
            messageExternalRepository.sendPlusOpenMq(plusOrderCreateEvent.getUserId(), ao.getConfigId());
        }
        return PlusAbyssResult.success(plusOrderCreateResp);
    }

    @Override
    public PlusAbyssResult<VirtualOrderCreateResp> createVirtualOrder(
            VirtualOrderCreateReq virtualOrderCreateReq) {
        if (virtualOrderCreateReq == null || virtualOrderCreateReq.getProductId() == null
                || StringUtils.isBlank(virtualOrderCreateReq.getProductSku())
                || virtualOrderCreateReq.getUserId() == null
                || virtualOrderCreateReq.getChannelId() == null
                || virtualOrderCreateReq.getProgramId() == null) {
            return PlusAbyssResult.error(VipErrorEnum.ERROR_100002.getCode(),
                    "商品id、商品sku、用户id、渠道id、方案id不能为空");
        }
        Integer userId = virtualOrderCreateReq.getUserId();
        if (virtualOrderCreateReq.getModelId() == null) {
            log.info("校验虚拟权益是否可购买,权益类型为空默认为生活权益：{}", userId);
            virtualOrderCreateReq.setModelId(PlusModelEnum.SHQY.getModelId());
        }
        String productSku = virtualOrderCreateReq.getProductSku();
        Integer modelId = virtualOrderCreateReq.getModelId();
        String redisKey =
                RedisConstantPrefix.COMMIT_VIRTUAL_ORDER_KEY + userId + productSku + modelId;
        boolean lock = redisLock.lock(redisKey, "1", 5);
        if (!lock) {
            log.info("用户:{} 5秒内重复提交虚拟订单 key:{}", userId, redisKey);
            throw new PlusAbyssException("短时间内不允许重复购买！");
        }
        VirtualOrderCreateEvent virtualOrderCreateEvent = orderApiConverter.toVirtualOrderCreateEvent(
                virtualOrderCreateReq);
        PlusOrderAo virtualOrder = plusOrderApplication.createVirtualOrder(virtualOrderCreateEvent);
        VirtualOrderCreateResp virtualOrderCreateResp = orderApiConverter.toVirtualOrderCreateResp(
                virtualOrder);
        return PlusAbyssResult.success(virtualOrderCreateResp);
    }

    /**
     * 创建虚拟商品权益订单
     */
    @Override
    public PlusAbyssResult<VirtualGoodsOrderCreateResp> createVirtualGoodsOrder(
            VirtualGoodsOrderCreateReq req) {
        if (req == null || req.getUserId() == null || req.getPlusOrderSn() == null
                || req.getRechargeAccount() == null || req.getChannelId() == null
                || req.getModelId() == null) {
            return PlusAbyssResult.error(VipErrorEnum.ERROR_100002.getCode(),
                    "用户id、会员订单号、充值账号、渠道id不能为空");
        }
        Integer userId = req.getUserId();
        String plusOrderSn = req.getPlusOrderSn();
        String redisKey = RedisConstantPrefix.COMMIT_LYFF_VIRTUAL_ORDER_KEY + plusOrderSn;
        boolean lock = redisLock.lock(redisKey, "1", 5);
        if (!lock) {
            log.info("用户:{} 5秒内重复提交权益0元发放虚拟订单 key:{}", userId, redisKey);
            throw new PlusAbyssException("短时间内不允许重复购买！");
        }
        VirtualGoodsOrderCreateEvent event = orderApiConverter.toVirtualGoodsOrderCreateEvent(req);
        // 创单
        PlusOrderAo virtualOrder = plusOrderApplication.createVirtualGoodsOrder(event);
        VirtualGoodsOrderCreateResp result = orderApiConverter.toVirtualGoodsOrderCreateResp(
                virtualOrder);
        return PlusAbyssResult.success(result);
    }

    @Override
    public PlusAbyssResult<PlusProductOrderCreateResp> createPlusProductOrder(
            PlusProductOrderCreateReq req) {
        if (req.getModelId() == null || req.getProductId() == null || StringUtils.isBlank(
                req.getProductSku()) || StringUtils.isBlank(req.getOrderSn())
                || req.getUserId() == null || req.getAddressId() == null
                || req.getProgramId() == null || req.getProductGoodsId() == null) {
            return PlusAbyssResult.error(VipErrorEnum.ERROR_100002.getCode(),
                    VipErrorEnum.ERROR_100002.getMessage());
        }
        Integer userId = req.getUserId();
        String productSku = req.getProductSku();
        String redisKey = RedisConstantPrefix.COMMIT_PLUS_PRODUCT_ORDER_KEY + userId + productSku;
        boolean lock = redisLock.lock(redisKey, "1", 5);
        if (!lock) {
            log.info("用户:{} 5秒内重复提交虚拟订单 key:{}", userId, redisKey);
            throw new PlusAbyssException("短时间内不允许重复购买！");
        }
        PlusProductOrderCreateEvent createEvent = orderApiConverter.toPlusProductOrderCreateEvent(
                req);
        PlusOrderAo plusProductOrder = plusOrderApplication.createPlusProductOrder(createEvent);
        PlusProductOrderCreateResp plusProductOrderCreateResp = orderApiConverter.toPlusProductOrderCreateResp(
                plusProductOrder);
        return PlusAbyssResult.success(plusProductOrderCreateResp);
    }

    @Override
    public PlusAbyssResult<VirtualCheckResultResp> canBuyVirtualProduct(
            VirtualOrderCreateReq virtualOrderCreateReq) {
        if (virtualOrderCreateReq == null || virtualOrderCreateReq.getProductId() == null
                || StringUtils.isBlank(virtualOrderCreateReq.getProductSku())
                || virtualOrderCreateReq.getUserId() == null
                || virtualOrderCreateReq.getChannelId() == null
                || virtualOrderCreateReq.getProgramId() == null) {
            return PlusAbyssResult.error(VipErrorEnum.ERROR_100002.getCode(),
                    "商品id、商品sku、用户id、渠道id、方案id不能为空");
        }
        Integer userId = virtualOrderCreateReq.getUserId();
        if (virtualOrderCreateReq.getModelId() == null) {
            log.info("校验虚拟权益是否可购买,权益类型为空默认为生活权益：{}", userId);
            virtualOrderCreateReq.setModelId(PlusModelEnum.SHQY.getModelId());
        }
        VirtualOrderCreateEvent virtualOrderCreateEvent = orderApiConverter.toVirtualOrderCreateEvent(
                virtualOrderCreateReq);
        VirtualCheckResultEntity result = plusOrderApplication.canBuyVirtualProduct(
                virtualOrderCreateEvent);
        VirtualCheckResultResp virtualCheckResultResp = orderApiConverter.toVirtualCheckResultResp(
                result);
        return PlusAbyssResult.success(virtualCheckResultResp);
    }

    @Override
    public PlusAbyssResult<ProductCheckResultResp> canBuyProduct(ProductCheckReq req) {
        if (req == null || StringUtils.isBlank(req.getProductSku()) || req.getProductId() == null
                || req.getUserId() == null || req.getProgramId() == null) {
            throw new PlusAbyssException("商品sku、商品id、权益id不能为空");
        }
        ProductCheckResultEntity entity = plusOrderApplication.canBuyProduct(
                orderApiConverter.toProductCheckEvent(req));
        return PlusAbyssResult.success(orderApiConverter.toProductCheckResultResp(entity));
    }

    @Override
    public PlusAbyssResult<Boolean> canBuyPlusProduct(ProductCheckReq req) {
        Boolean canBuy = plusOrderApplication.canBuyPlusProduct(
                orderApiConverter.toProductCheckEvent(req));
        return PlusAbyssResult.success(canBuy);
    }

    @Override
    public PlusAbyssResult<PlusOrderCancelResp> cancelOrder(PlusOrderCancelReq plusOrderCancelReq) {
        PlusOrderCancelEvent plusOrderCancelEvent = orderApiConverter.toPlusOrderCancelEvent(
                plusOrderCancelReq);
        PlusOrderCancelAo cancelAo = plusOrderApplication.cancelOrderApply(plusOrderCancelEvent);
        return PlusAbyssResult.success(orderApiConverter.toPlusOrderCancelResp(cancelAo));
    }

    @Override
    public PlusAbyssResult updOrderStateByCustomer(UpdOrderStateReq req) {
        UpdOrderStateEvent updOrderStateEvent = orderApiConverter.toUpdOrderStateEvent(req);
        plusOrderApplication.updOrderStateByCustomer(updOrderStateEvent);
        return PlusAbyssResult.success();
    }

    /**
     * 取消续费(小额月卡)
     */
    @Override
    public PlusAbyssResult cancelRenew(CancelRenewReq req) {
        CancelRenewEvent event = orderApiConverter.toCancelRenewInfoResp(req);
        // 取消续费
        plusOrderApplication.cancelRenew(event);
        return PlusAbyssResult.success();
    }

    @Override
    public PlusAbyssResult<PlusOrderCancelResp> checkPreCancel(
            PlusOrderCancelReq plusOrderCancelReq) {
        log.info("取消会员前校验入参:{}", JSON.toJSONString(plusOrderCancelReq));
        PlusOrderCancelEvent plusOrderCancelEvent = orderApiConverter.toPlusOrderCancelEvent(
                plusOrderCancelReq);
        // 20240309 zjf 会员订单取消前需要获取用户最近一次人脸识别结果给客服展示
        plusOrderCancelEvent.setNeedUserFaceAuth(true);
        PlusOrderCancelAo plusOrderAo = plusOrderApplication.checkPreCancel(plusOrderCancelEvent);
        PlusOrderCancelResp plusOrderCancelResp = orderApiConverter.toPlusOrderCancelResp(
                plusOrderAo);
        return PlusAbyssResult.success(plusOrderCancelResp);
    }

    @Override
    public PlusAbyssResult<PlusOrderDeductResp> deduct(PlusDeductForActiveReq plusOrderDeductReq) {
        PlusDeductEvent deductEvent = orderApiConverter.toDeductEvent(plusOrderDeductReq);
        PlusOrderDeductAo plusOrderDeductAo = plusOrderApplication.deduct(deductEvent);
        PlusOrderDeductResp plusOrderDeductResp = orderApiConverter.toPlusOrderDeductResp(
                plusOrderDeductAo);
        return PlusAbyssResult.success(plusOrderDeductResp);
    }

    @Override
    public PlusAbyssResult payCallBack(PlusPayCallbackReq plusPayCallbackReq) {
        PlusOrderPayCallbackEvent event = orderApiConverter.toPlusOrderPayCallbackEvent(
                plusPayCallbackReq);
        PlusPayCallbackAo ao = plusOrderApplication.payCallBack(event);
        if (ao.isOpenCard()) {
            // 发送开通成功mq（这个非过度方案，切勿删除）
            PlusOrderMessageMqOutEvent orderMessageOutEvent = orderApiConverter.toPlusOrderMessageMqOutEvent(
                    1, event, ao);
            messageExternalRepository.sendPlusMq(orderMessageOutEvent);
        }
        return PlusAbyssResult.success();
    }

    @Override
    public PlusAbyssResult unPayCancelCallBack(String plusOrderSn) {
        plusOrderApplication.unPayCancelCallBack(plusOrderSn);
        return PlusAbyssResult.success();
    }

    /**
     * 会员单与业务订单建立绑定关系
     */
    @Override
    public PlusAbyssResult buildOrderRelation(
            PlusOrderRelationCreateReq plusOrderRelationCreateReq) {
        PlusOrderRelationCreateEvent event = orderApiConverter.toPlusOrderRelationCreateEvent(
                plusOrderRelationCreateReq);
        plusOrderApplication.buildOrderRelation(event);
        return PlusAbyssResult.success();
    }

    @Override
    public PlusAbyssResult<PlusRenewInfoResp> getRenewInfo(PlusRenewInfoReq plusRenewInfoReq) {
        PlusRenewInfoEvent plusRenewInfoEvent = orderApiConverter.toPlusOrderRenewInfoEvent(
                plusRenewInfoReq);
        PlusRenewInfoAo renewInfo = queryApplication.getRenewInfo(plusRenewInfoEvent);
        PlusRenewInfoResp plusRenewInfoResp = orderApiConverter.toPlusRenewInfoResp(renewInfo);
        return PlusAbyssResult.success(plusRenewInfoResp);
    }

    @Override
    public PlusAbyssResult<List<PlusOrderRelationResp>> getOrderRelation(PlusOrderRelationReq req) {
        if (req == null || CollectionUtils.isEmpty(req.getOrderSns()) || req.getOrderType() == null
                || req.getBusinessType() == null) {
            throw new PlusAbyssException("orderSns、orderType、businessType不能为空");
        }
        PlusOrderRelationEvent plusOrderRelationEvent = orderApiConverter.toPlusOrderRelationEvent(
                req);
        List<PlusOrderRelationEntity> orderRelation = queryApplication.getOrderRelation(
                plusOrderRelationEvent);
        List<PlusOrderRelationResp> list = orderApiConverter.toPlusOrderRelationResp(orderRelation);
        return PlusAbyssResult.success(list);
    }

    @Override
    public PlusAbyssResult orderRefundApply(PlusOrderRefundApplyReq req) {
        log.info("订单退卡申请入参：{}", JSON.toJSONString(req));
        if (req.getDiscountRate() != null && (req.getDiscountRate().compareTo(BigDecimal.ZERO) <= 0
                || req.getDiscountRate().compareTo(BigDecimal.ONE) > 0)) {
            return PlusAbyssResult.error("退款比例须>0且<=1");
        }
        PlusOrderRefundApplyEvent event = orderApiConverter.toPlusOrderRefundApplyEvent(req);
        event.setRefundRate(req.getDiscountRate());
        orderRefundApplyApplication.orderRefundApply(event);
        return PlusAbyssResult.success();
    }

    @Override
    public PlusAbyssResult saveNoticeTask(CreateOrderNoticeTaskReq req) {
        log.info("保存订单通知任务入参：{}", JSON.toJSONString(req));
        PlusOrderNoticeEvent event = orderApiConverter.toPlusOrderNoticeEvent(req);
        noticeApplication.saveNoticeTask(event);
        return PlusAbyssResult.success();
    }

    @Override
    public PlusAbyssResult<PlusOrderPayInfoResp> getOrderPayInfo(String orderSn) {
        return PlusAbyssResult.success(orderApiConverter.toPlusOrderPayInfoResp(
                plusOrderApplication.getOrderPayInfo(orderSn)));
    }

    @Override
    public PlusAbyssResult<PlusOrderRefundInfoResp> getPlusOrderRefundApplyInfo(String orderSn) {
        return PlusAbyssResult.success(orderApiConverter.toPlusOrderRefundInfoResp(plusOrderRefundInfoRepository.getLastOneByOrderSn(orderSn)));
    }

}
