package com.juzifenqi.plus.module.order.model.contract.entity.callback.pay;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 新支付系统支付结果回调
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/2 20:02
 */
@Data
public class NewPayResultCallbackEntity implements Serializable {
    private static final long serialVersionUID = 42L;

    /**
     *  主动支付才有为支付流水号
     */
    private String paySerialNumber;

    /**
     *  主动支付为收银台流水号 ｜ 代扣为支付流水号
     */
    private String serialNumber;

    /**
     * 业务请求流水号
     */
    private String thirdPayNum;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 状态 S：支付成功 F：支付失败
     */
    private String state;

    /**
     * 渠道号
     */
    private String application;

    /**
     * 支付错误码
     */
    private String payErrorCode;

    /**
     * 支付错误描述
     */
    private String payErrorMsg;

    /**
     * 支付产品编码
     */
    private String payProductCode;

    /**
     * 银行卡号
     */
    private String ctCardNo;

    /**
     * 交易金额（元）
     */
    private BigDecimal amount;
}
