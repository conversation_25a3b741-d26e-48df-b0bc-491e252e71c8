package com.juzifenqi.plus.module.order.application;

import com.juzifenqi.plus.dto.req.admin.PlusOrderDetailQueryReq;
import com.juzifenqi.plus.dto.req.admin.PlusOrderInfoQueryReq;
import com.juzifenqi.plus.enums.PlusCancelTypeEnum;
import com.juzifenqi.plus.module.asserts.model.contract.entity.ProductCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.VirtualCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.VirtualGoodsCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.event.ProductCheckEvent;
import com.juzifenqi.plus.module.asserts.model.event.VirtualGoodsCheckEvent;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderCancelAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderDeductAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderDetailAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderInfoAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderPayInfoAo;
import com.juzifenqi.plus.module.order.application.ao.PlusPayCallbackAo;
import com.juzifenqi.plus.module.order.model.contract.entity.MemberPlusRenewPlanEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.OrderCancelFirstPayEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderCancelEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusProductOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.PayCallbackEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.pay.NewPayResultCallbackEntity;
import com.juzifenqi.plus.module.order.model.event.CancelRenewEvent;
import com.juzifenqi.plus.module.order.model.event.CreateDeductPlanEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;
import com.juzifenqi.plus.module.order.model.event.order.*;

import java.util.List;

/**
 * 订单application
 *
 * <AUTHOR>
 */
public interface IPlusOrderApplication {

    /**
     * 会员下单
     */
    PlusOrderAo createPlusOrder(PlusOrderCreateEvent plusOrderCreateEvent);

    /**
     * 虚拟权益下单
     */
    PlusOrderAo createVirtualOrder(VirtualOrderCreateEvent createEvent);

    /**
     * 虚拟商品权益下单
     */
    PlusOrderAo createVirtualGoodsOrder(VirtualGoodsOrderCreateEvent createEvent);

    /**
     * 会员商品下单
     */
    PlusOrderAo createPlusProductOrder(PlusProductOrderCreateEvent plusProductOrderCreateEvent);

    /**
     * 检查虚拟权益是否能购买
     */
    VirtualCheckResultEntity canBuyVirtualProduct(VirtualOrderCreateEvent createEvent);

    /**
     * 检查用户是否可以领取虚拟商品权益
     * <p>目前是权益0元发放在用</p>
     */
    VirtualGoodsCheckResultEntity canBuyVirtualGoods(VirtualGoodsCheckEvent event);

    /**
     * 检查用户是否可以购买会员商品权益,权益分类限制校验
     * <p>目前是0元商品在用</p>
     */
    ProductCheckResultEntity canBuyProduct(ProductCheckEvent event);

    /**
     * 能否购买会员商品
     * <p>目前是一元购和会员商品权益内的商品</p>
     */
    Boolean canBuyPlusProduct(ProductCheckEvent event);

    /**
     * 支付回调-会员开通
     */
    PlusPayCallbackAo payCallBack(PlusOrderPayCallbackEvent plusOrderPayCallbackEvent);

    /**
     * 会员订单未支付超时取消
     */
    void unPayCancelCallBack(String plusOrderSn);

    /**
     * 取消订单
     * <p>有条件、无条件、按比例、急速、延迟、未过期、过期、批量取消</p>
     */
    PlusOrderCancelAo cancelPlusOrder(PlusOrderCancelEvent plusOrderCancelEvent);

    /**
     * 取消重复支付订单
     */
    void cancelRepOrder(PlusOrderCancelEvent plusOrderCancelEvent,
            PlusOrderCancelEntity cancelEntity);

    /**
     * 客服操作变更会员订单状态
     * <p>支付成功：由待支付变更为支付成功，用户线下转账给公司，可能是用户已注销桔多多app等原因</p>
     * <p>取消：由支付成功变更为取消，线下转账给用户，可能是用户的卡受限等原因</p>
     */
    void updOrderStateByCustomer(UpdOrderStateEvent event);

    /**
     * 取消订单预检查
     */
    PlusOrderCancelAo checkPreCancel(PlusOrderCancelEvent plusOrderCancelEvent);

    /**
     * 取消规则校验（只校验取消规则）
     */
    void cancelPreCheckRule(PlusCancelTypeEnum cancelType,
            PlusOrderCancelEvent plusOrderCancelEvent);

    /**
     * 生成划扣计划
     */
    void createDuctPlan(CreateDeductPlanEvent createDeductPlanEvent);

    /**
     * 会员订单划扣（单个会员类型）
     */
    PlusOrderDeductAo deduct(PlusDeductEvent event);

    /**
     * 批量延时划扣
     * <p>非融担卡</p>
     */
    void delayBatchDeduct();

    /**
     * 会员单与业务订单建立绑定关系
     */
    void buildOrderRelation(PlusOrderRelationCreateEvent plusOrderRelationCreateEvent);

    /**
     * 解除会员单与借款单的绑定关系
     */
    void clearOrderRelation(Integer businessType, String plusOrderSn);

    /**
     * 取消订单的续费(小额月卡)
     */
    void cancelRenew(CancelRenewEvent event);

    /**
     * 根据续费计划创建订单
     */
    Boolean createPlusOrderByRenewPlan(MemberPlusRenewPlanEntity renewPlan);

    /**
     * 根据续费计划创建订单
     */
    void createPlusOrderByRenewPlans(List<MemberPlusRenewPlanEntity> renewPlans);

    /**
     * 处理后付款开通失败订单
     */
    void invalidOrder();

    /**
     * 浩瀚后台-会员订单列表
     */
    PageResultEntity<PlusOrderInfoAo> getPlusOrderList(PlusOrderInfoQueryReq req);

    /**
     * 浩瀚后台-订单列表-查看详情
     */
    PlusOrderDetailAo getPlusOrderDetails(PlusOrderDetailQueryReq req);

    /**
     * 获取会员订单某个权益下单的会员商品订单信息
     */
    List<PlusProductOrderEntity> getProductOrderList(String plusOrderSn, Integer modelId);

    /**
     * 会员过期取消待支付的后付款订单
     */
    void expireCancelWaitAfterPayOrder(List<String> orderSn);

    /**
     * 还款卡续费
     */
    void repayRenew(Integer userId, Integer channelId);

    /**
     * 支付结果回调处理
     */
    void payResultCallback(PayCallbackEntity entity);

    /**
     * 订单中心退款结果回调
     */
    void orderRefundCallback(OrderCancelFirstPayEntity entity);

    /**
     * 订单重提标识查询
     */
    void resubmitFlagVerify();

    /**
     * 会员订单取消申请
     */
    PlusOrderCancelAo cancelOrderApply(PlusOrderCancelEvent event);

    /**
     * 订单中心退款通知
     */
    void orderRefundNotify(OrderRefundNotifyEntity entity);


    /**
     * 订单发起第二笔退款
     */
    void orderSecondRefund(OrderRefundSecondEvent event);

    /**
     * 新支付系统支付结果回调处理
     */
    void newPayResultCallback(NewPayResultCallbackEntity entity);

    /**
     * 获取支付请求信息
     */
    PlusOrderPayInfoAo getOrderPayInfo(String orderSn);


    /**
     * 重置划扣计划
     */
    void resetDuctPlan(String orderNo, Integer configId, Integer programId);

    /**
     * 订单重试第二次退款
     */
    void retryOrderSecondRefund(Long refundInfoId);
}
