package com.juzifenqi.plus.module.order.model;

import com.juzifenqi.plus.enums.LogNodeEnum;
import com.juzifenqi.plus.module.asserts.model.contract.entity.ProductCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.VirtualCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.VirtualGoodsCheckResultEntity;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailExtPo;
import com.juzifenqi.plus.module.order.model.contract.entity.*;
import com.juzifenqi.plus.module.order.model.event.CreateDeductPlanEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderPayCallbackEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRelationCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusProductOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.UpdOrderStateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualGoodsOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualOrderCreateEvent;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 订单领域能力层
 */
public interface PlusOrderModel {

    /**
     * 会员下单
     */
    PlusOrderEntity createPlusOrder(PlusOrderCreateEvent plusOrderCreateEvent,
            PlusProgramEntity programEntity);


    PlusDiscountEntity getPlusDiscount(Integer userId, Integer configId, Integer channelId, Integer sceneCode);


    /**
     * 虚拟权益下单
     */
    OrderEntity createVirtualOrder(VirtualOrderCreateEvent createEvent,
            VirtualCheckResultEntity entity);

    /**
     * 虚拟商品权益下单
     */
    OrderEntity createVirtualGoodsOrder(VirtualGoodsOrderCreateEvent createEvent,
            VirtualGoodsCheckResultEntity entity);

    /**
     * 1.下单
     */
    OrderEntity createPlusProductOrder(PlusProductOrderCreateEvent createEvent,
            ProductCheckResultEntity checkResult);

    /**
     * 支付成功开通会员处理
     */
    void payCallBackForOpenCard(PlusOrderPayCallbackEvent plusPayCallbackEvent,
            PlusOrderEntity plusOrderEntity);

    /**
     * 取消订单
     * <p>有条件、无条件、急速、延迟、未过期、过期、批量取消</p>
     */
    void cancelOrder(PlusOrderCancelEvent plusOrderCancelEvent, PlusOrderEntity plusOrderEntity,
            PlusOrderCancelEntity entity, List<MemberPlusInfoDetailExtPo> moveList);

    /**
     * 取消重复支付订单
     */
    void cancelRepOrder(PlusOrderCancelEvent event, PlusOrderCancelEntity entity,
            PlusOrderEntity plusOrderEntity);

    /**
     * 获取取消操作节点
     */
    LogNodeEnum getCancelOrderNode(PlusOrderCancelEvent event);

    /**
     * 获取备注
     */
    String getCancelRemark(PlusOrderEntity plusOrderInfo, PlusOrderCancelEvent event);

    /**
     * 客服操作变更会员订单状态
     */
    void updOrderByCustomer(UpdOrderStateEvent event, PlusOrderEntity order,
            List<MemberPlusInfoDetailExtPo> moveList);

    /**
     * 会员订单未支付超时取消
     */
    void unPayCancelCallBack(String plusOrderSn);

    /**
     * 合同签署
     *
     * @param flag 1=会员开通成功签署合同
     */
    void signContract(PlusOrderEntity plusOrderEntity, PlusOrderCreateEvent event, Integer flag);

    /**
     * 会员单与业务订单建立绑定关系
     */
    void buildOrderRelation(PlusOrderRelationCreateEvent plusOrderRelationCreateEvent);

    /**
     * 解除会员单与业务订单的绑定关系
     */
    void clearOrderRelation(Integer businessType, String plusOrderSn);

    /**
     * 生成划扣计划
     */
    void createDuctPlan(CreateDeductPlanEvent createDeductPlanEvent);

    /**
     * 订单划扣前置处理
     */
    void preDeduct(PlusDeductEvent deductEvent);

    /**
     * 订单划扣（旧）
     */
    @Deprecated
    PlusOrderDeductResEntity deductOld(PlusDeductEvent deductEvent);

    /**
     * 订单划扣（新）
     */
    PlusOrderDeductResEntity deduct(PlusDeductEvent deductEvent);

    /**
     * 取消续费
     */
    void cancelRenew(PlusOrderEntity plusOrderEntity, String remark);

    /**
     * 后付款开通会员处理
     */
    PlusOpenResultEntity afterOrderForOpenCard(PlusOrderEntity plusOrderEntity,
            PlusOrderCreateEvent event, PlusProgramEntity program);

    /**
     * 保存失效订单
     */
    void saveInvalidOrder(Integer userId, Integer programId);

    /**
     * 计算订单退款金额（包含扣减差价计算）
     */
    PlusOrderCancelEntity calOrderRefundAmount(PlusOrderEntity order, PlusOrderCancelEvent event);

    /**
     * 处理后付款开通失败订单
     */
    void invalidOrder();

    /**
     * 会员过期取消待支付的后付款订单
     */
    void expireCancelWaitAfterPayOrder(List<String> orderSn);

    /**
     * 订单重提标识查询
     */
    void resubmitFlagVerify();

    /**
     * 补偿签署
     */
    boolean handleSignRdzxContract(PlusOrderEntity order, PlusRdzxRelationDetailEntity relationDetail);


    void updateOrderRefundAmount(String plusOrderSn, BigDecimal refundAmount);
}
