package com.juzifenqi.plus.module.asserts.model.impl;

import com.alibaba.fastjson.JSON;
import com.groot.utils.core.date.LocalDateTimeUtils;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.dto.req.detail.ProfitsDetailReq;
import com.juzifenqi.plus.dto.req.detail.ProfitsMergeDetailReq;
import com.juzifenqi.plus.dto.req.order.PlusOrderQueryReq;
import com.juzifenqi.plus.enums.BusinessTypeEnum;
import com.juzifenqi.plus.enums.ChannelEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.MemberPlusInfoStatusEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.enums.RenewStateEnum;
import com.juzifenqi.plus.enums.VipErrorEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.MemberProfitsQueryModel;
import com.juzifenqi.plus.module.asserts.model.ProfitDetailModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoEntity;
import com.juzifenqi.plus.module.asserts.model.converter.IProfitsHandlerConverter;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitDetailBasicEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitFlagDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitMergeCommonDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitMergeDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitModelBasicDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitOldDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitPlusDetailInfoEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitProgramBasicDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitVirtualProductTypeEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitVirtualProductTypeLevelEntity;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleProfitQueryEvent;
import com.juzifenqi.plus.module.asserts.model.impl.strategy.profits.ProfitHandlerContext;
import com.juzifenqi.plus.module.common.IAuthExternalRepository;
import com.juzifenqi.plus.module.order.model.IPlusOrderShuntQueryModel;
import com.juzifenqi.plus.module.order.model.PlusOrderQueryModel;
import com.juzifenqi.plus.module.order.model.PlusOrderSnapshtoQueryModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderExtInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderShuntEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRenewInfoEntity;
import com.juzifenqi.plus.module.order.model.event.PlusRenewInfoEvent;
import com.juzifenqi.plus.module.program.model.IPlusChannelManagerQueryModel;
import com.juzifenqi.plus.module.program.model.IPlusProfitQueryModel;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.contract.IPlusProModelRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.jzfq.auth.core.entity.AuthApproval;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * 虚拟商品类权益
 *
 * <AUTHOR>
 * @date 2024-05-29 10:36
 */
@Service
@Slf4j
public class ProfitDetailModelImpl implements ProfitDetailModel {

    private final IProfitsHandlerConverter converter = IProfitsHandlerConverter.instance;

    @Autowired
    private MemberPlusQueryModel          plusQueryModel;
    @Autowired
    private IPlusProgramQueryModel        programQueryModel;
    @Autowired
    private IAuthExternalRepository       authExternalRepository;
    @Autowired
    private IPlusChannelManagerQueryModel channelManagerQueryModel;
    @Autowired
    private ProfitHandlerContext          handlerContext;
    @Autowired
    private IPlusOrderRepository          plusOrderRepository;
    @Autowired
    private IPlusProModelRepository       plusProModelRepository;
    @Autowired
    private PlusOrderSnapshtoQueryModel   plusOrderSnapshtoQueryModel;
    @Autowired
    private PlusOrderQueryModel           orderQueryModel;
    @Autowired
    private MemberProfitsQueryModel       profitQueryModel;
    @Autowired
    private IPlusProfitQueryModel         plusProfitQueryModel;
    @Autowired
    private IPlusOrderShuntQueryModel     shuntQueryModel;
    @Resource
    private ConfigProperties configProperties;

    /**
     * 用户权益页详情
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/6/14 13:41
     */
    public ProfitDetailEntity getProfitsDetail(ProfitsDetailReq req) {
        ProfitDetailEntity entity = new ProfitDetailEntity();
        //返回数据封装
        log.info("用户权益页数据处理开始：userId：{}，channel:{},configId:{}", req.getUserId(),
                req.getChannelId(), req.getConfigId());
        //1、处理用户会员信息
        entity.setPlusDetailInfo(getPlusDetailInfo(req));
        //2、处理会员方案基本信息
        entity.setBasicInfo(getProgramBasicInfo(req, entity.getPlusDetailInfo()));
        //3、标识信息处理
        entity.setFlagInfo(getFlagInfo(req, entity));
        //4、权益信息处理
        entity.setProfitInfo(getProfitInfo(req, entity, entity.getPlusDetailInfo()));
        //5、续费信息 后续考虑要不要放进来
        log.info("用户权益页数据处理结束：userId：{}，channel:{},result:{}", req.getUserId(), req.getChannelId(),
                JSON.toJSONString(entity));
        return entity;
    }

    @Override
    public ProfitMergeDetailEntity getProfitMergeDetail(ProfitsMergeDetailReq req) {
        Integer userId = req.getUserId();
        Integer channelId = req.getChannelId();
        // 查询用户所有会员，按会员订单创建时间倒序（桔享卡、加速卡、固额卡、小额月卡）
        List<MemberPlusInfoDetailEntity> infos = plusQueryModel.getMemberPlusInfoList(userId,
                channelId, PlusConstant.MERGE_CARD_LIST);
        log.info("会员合并权益页，获取当期的所有会员信息开始：userId:{},infos:{}", userId, JSON.toJSONString(infos));
        if (CollectionUtils.isEmpty(infos)) {
            return null;
        }
        //处理权益数据
        List<ProfitDetailEntity> profitInfos = new ArrayList<>();
        Date date = new Date();
        //当期会员查询权益数据
        infos.stream().filter(detail -> detail.getJxEndTime().after(date) && detail.getJxStartTime()
                .before(date)).forEach(detailInfo -> {
            ProfitsDetailReq param = new ProfitsDetailReq();
            param.setConfigId(detailInfo.getConfigId());
            param.setChannelId(channelId);
            param.setUserId(userId);
            ProfitDetailEntity detail = getProfitsDetail(param);
            profitInfos.add(detail);
        });
        log.info("会员合并权益页，获取当期的所有会员权益数据：userId:{},profitInfos:{}", userId,
                JSON.toJSONString(profitInfos));
        // 20230919 zjf 小额月卡不在当前周期范围内，需要手动添加进来
        if (CollectionUtils.isEmpty(profitInfos) || profitInfos.stream().noneMatch(
                e -> e.getBasicInfo() != null
                        && e.getBasicInfo().getConfigId() == JuziPlusEnum.XEYK_CARD.getCode())) {
            MemberPlusInfoDetailEntity xeykDetail = infos.stream()
                    .filter(e -> e.getConfigId() == JuziPlusEnum.XEYK_CARD.getCode())
                    .min(Comparator.comparing(MemberPlusInfoDetailEntity::getCreateTime))
                    .orElse(null);
            log.info("合并权益页，当前周期没有小额月卡会员，查询是否有小额月卡会员身份信息结果：{}", JSON.toJSONString(xeykDetail));
            if (xeykDetail != null) {
                ProfitsDetailReq param = new ProfitsDetailReq();
                param.setConfigId(xeykDetail.getConfigId());
                param.setChannelId(channelId);
                param.setUserId(userId);
                ProfitDetailEntity detail = getProfitsDetail(param);
                profitInfos.add(detail);
            }
        }
        if (CollectionUtils.isEmpty(profitInfos)) {
            return null;
        }
        //封装公共信息
        ProfitMergeCommonDetailEntity commonInfo = getCommonInfo(userId, infos, profitInfos, req);
        log.info("会员合并权益页，获取当期的公共信息：userId:{},commonInfo:{}", userId,
                JSON.toJSONString(commonInfo));
        //处理返回值
        ProfitMergeDetailEntity mergeDetailEntity = new ProfitMergeDetailEntity();
        mergeDetailEntity.setCommonInfo(commonInfo);
        mergeDetailEntity.setProfitInfos(profitInfos);
        log.info("会员合并权益页，获取当期的所有会员信息结束：userId:{},result:{}", userId,
                JSON.toJSONString(mergeDetailEntity));
        return mergeDetailEntity;
    }

    @Override
    public ProfitOldDetailEntity getProfitsOldDetail(ProfitsDetailReq req) {
        Integer userId = req.getUserId();
        Integer configId = req.getConfigId();
        List<MemberPlusInfoDetailEntity> list = plusQueryModel.getDetailByUserId(userId, configId);
        if (CollectionUtils.isEmpty(list)) {
            throw new PlusAbyssException("您不是会员,请先开通");
        }
        Date date = new Date();
        MemberPlusInfoDetailEntity memberPlusInfo = list.stream()
                .filter(e -> e.getJxEndTime().after(date) && e.getJxStartTime().before(date))
                .findFirst().orElse(null);
        if (memberPlusInfo == null) {
            throw new PlusAbyssException("您不是会员,请先开通");
        }
        if (memberPlusInfo.getJxStatus() == null || memberPlusInfo.getJxStatus() != 1) {
            throw new PlusAbyssException("您的会员已过期");
        }
        PlusProgramEntity program = programQueryModel.getById(memberPlusInfo.getProgramId());
        if (program == null || program.getProgramId() == null) {
            throw new PlusAbyssException("未获取到会员方案");
        }
        ProfitOldDetailEntity entity = converter.toProfitOldDetailEntity(program);
        //根据方案id查询关联的权益信息，按照顺序排列 保持不变
        List<PlusProModelEntity> rights = plusProModelRepository.getProModelByProgramIdOrderBySort(
                memberPlusInfo.getProgramId());
        Map<String, Object> memberDetailProfits = new HashMap<>(8);
        rights.forEach(proModel -> {
            HandleProfitQueryEvent queryEvent = converter.toHandleProfitQueryEvent(req,
                    proModel.getModelId(), memberPlusInfo.getProgramId(),
                    memberPlusInfo.getOrderSn());
            // 老权益页目前只有：还款卡、桔省卡在使用，不需要处理提额权益
            if (Objects.equals(proModel.getModelId(), PlusModelEnum.HYTE.getModelId())) {
                return;
            }
            // 获取权益的基本信息 保持不变
            ProfitModelBasicDetailEntity profitBasicInfo = plusProfitQueryModel.getProfitBasicInfo(
                    queryEvent);
            // 获取权益的权益数据
            Map<String, Object> profitForDetailOld = handlerContext.getProfitForDetailOld(
                    queryEvent);
            profitBasicInfo.setData(profitForDetailOld);
            if (!CollectionUtils.isEmpty(profitBasicInfo.getData())) {
                memberDetailProfits.put(profitBasicInfo.getShortPY(), profitBasicInfo);
            }
        });
        entity.setProgramId(memberPlusInfo.getProgramId());
        entity.setRightsNum(rights.size());
        entity.setEndTime(LocalDateTimeUtils.parseDateToString(memberPlusInfo.getJxEndTime(),
                LocalDateTimeUtils.DATE_FORMAT_SHORT_S));
        entity.setOrderSn(memberPlusInfo.getOrderSn());
        // 最新开通会员的创建时间
        MemberPlusInfoDetailEntity maxDetail = list.stream()
                .max(Comparator.comparing(MemberPlusInfoDetailEntity::getId)).orElse(null);
        if (maxDetail != null) {
            //权益时间开始时间+6天
            Date dateAfterDays = LocalDateTimeUtils.getDateAfterDays(maxDetail.getCreateTime(), 6);
            entity.setProfitEndTime(LocalDateTimeUtils.parseDateToString(dateAfterDays,
                    LocalDateTimeUtils.DATE_FORMAT_SHORT_S));
        }
        entity.setMemberDetailProfits(memberDetailProfits);
        //设置认证状态
        AuthApproval authApproval = authExternalRepository.getAuthState(userId,
                req.getChannelId());
        entity.setAuthState(authApproval.getAuthState());

        // 后付款待支付订单
        PlusOrderEntity waitPayOrder;
        if (configProperties.authChannel.contains(req.getChannelId().toString())) {
            waitPayOrder = plusOrderRepository.getUserWaitPayOrderAfterAuthTime(userId, configId, authApproval.getSuccessTime());
        } else {
            waitPayOrder = plusOrderRepository.getUserWaitPayOrder(userId, configId);
        }
        if (waitPayOrder != null) {
            entity.setOrderAfterPay(waitPayOrder.getOrderSn());
        }
        //设置虚拟商品信息
        HandleProfitQueryEvent paramVo = new HandleProfitQueryEvent();
        paramVo.setProgramId(memberPlusInfo.getProgramId());
        paramVo.setUserId(userId);
        paramVo.setOrderSn(memberPlusInfo.getOrderSn());
        entity.setVirtualProfits(profitQueryModel.getProfitVirtualProductTypeEntityList(paramVo));
        return entity;
    }

    /**
     * 处理合并公共信息
     */
    /**
     * 处理公共信息
     */
    private ProfitMergeCommonDetailEntity getCommonInfo(Integer userId,
            List<MemberPlusInfoDetailEntity> infos, List<ProfitDetailEntity> profitInfos,
            ProfitsMergeDetailReq dto) {
        // 处理公共信息
        ProfitMergeCommonDetailEntity commonInfo = new ProfitMergeCommonDetailEntity();
        // 先获取包含提额权益的权益页信息
        AtomicReference<ProfitModelBasicDetailEntity> amountProfit = new AtomicReference<>(null);
        profitInfos.forEach(entity -> entity.getProfitInfo().getModelBasicInfoVos().stream()
                .filter(modelBasicInfoVo -> modelBasicInfoVo.getModelId()
                        == PlusModelEnum.HYTE.getModelId()).findAny().ifPresent(amountProfit::set));
        ProfitModelBasicDetailEntity modelBasicInfoVo = amountProfit.get();
        log.info("会员合并权益页，处理公共信息，获取额度信息：targetAmount:{}", JSON.toJSONString(modelBasicInfoVo));
        if (modelBasicInfoVo != null && modelBasicInfoVo.getData().get("canLoanAmount") != null) {
            // 会员总提升额度
            commonInfo.setVipCreditSumAll(
                    (BigDecimal) modelBasicInfoVo.getData().get("canLoanAmount"));
            commonInfo.setOperate(String.valueOf(modelBasicInfoVo.getData().get("operate")));
        }
        // 设置会员结束时间
        infos.stream().max(Comparator.comparing(MemberPlusInfoDetailEntity::getJxEndTime))
                .ifPresent(endTimeInfo -> commonInfo.setEndTime(
                        LocalDateTimeUtils.parseDateToString(endTimeInfo.getJxEndTime(),
                                LocalDateTimeUtils.DATE_FORMAT_SHORT_S)));
        Date dateAfterDays = LocalDateTimeUtils.getDateAfterDays(infos.get(0).getCreateTime(), 7);
        // 格式化加速结束时间
        commonInfo.setExpediteEndTime(LocalDateTimeUtils.parseDateToString(dateAfterDays,
                LocalDateTimeUtils.DATE_FORMAT_SHORT_S));
        // 当期周期开通的订单
        Date date = new Date();
        List<MemberPlusInfoDetailEntity> collect = infos.stream()
                .filter(detail -> detail.getJxEndTime().after(date) && detail.getJxStartTime()
                        .before(date)).collect(Collectors.toList());
        commonInfo.setCurrentOrderSn(collect.get(0).getOrderSn());
        // 待支付后付款订单
        PlusOrderEntity waitPayOrder = null;
        // 查询后付款待支付会员订单
        List<PlusOrderEntity> userWaitPayOrderList = orderQueryModel.getUserWaitPayOrderList(userId,
                dto.getChannelId());
        if (!CollectionUtils.isEmpty(userWaitPayOrderList)) {
            // 20230919 zjf 多个待支付的后付款，按创单时间取最早的一个展示
            waitPayOrder = userWaitPayOrderList.stream()
                    .min(Comparator.comparing(PlusOrderEntity::getCreateTime)).get();
            commonInfo.setOrderAfterPay(waitPayOrder.getOrderSn());
            commonInfo.setPayType(waitPayOrder.getPayType());
            //剩余支付金额
            if (waitPayOrder.getPayAmount() != null) {
                commonInfo.setSurplusPayAmount(waitPayOrder.getOrderAmount().subtract(waitPayOrder.getPayAmount()));
            } else {
                commonInfo.setSurplusPayAmount(waitPayOrder.getOrderAmount());
            }
        }
        // 20230919 zjf 处理续费信息
        renewHandle(dto, commonInfo, profitInfos);
        // 20240704 zjf 计划分流id
        commonInfo.setPlanSupplierId(getPlanSupplierId(waitPayOrder));
        return commonInfo;
    }

    /**
     * 获取计划分流id
     * <p>是分流的卡及商城渠道才获取</p>
     */
    private Integer getPlanSupplierId(PlusOrderEntity order) {
        if (order == null
                || StringUtils.isBlank(order.getOrderSn())
                || !PlusConstant.SHUNT_CARD_LIST.contains(order.getConfigId())) {
            return null;
        }
        PlusOrderShuntEntity shunt = shuntQueryModel.getByOrderSn(order.getOrderSn());
        return shunt == null ? null : shunt.getPlanInSupplier();
    }

    /**
     * 处理续费信息
     */
    private void renewHandle(ProfitsMergeDetailReq dto, ProfitMergeCommonDetailEntity commonInfoVo,
            List<ProfitDetailEntity> profitInfos) {
        // 20230919 zjf 小额月卡取消续费入口，目前只处理商城渠道
        if (!Objects.equals(dto.getChannelId(), ChannelEnum.MALL.getCode())) {
            log.info("处理小额月卡入口,非商城渠道：{}，{}", dto.getUserId(), dto.getChannelId());
            return;
        }
        ProfitDetailEntity xeyk = profitInfos.stream().filter(e -> e.getBasicInfo() != null
                        && e.getBasicInfo().getConfigId() == JuziPlusEnum.XEYK_CARD.getCode()).findFirst()
                .orElse(null);
        if (xeyk == null) {
            log.info("处理小额月卡入口,用户非小额月卡会员身份：{}", dto.getUserId());
            return;
        }
        PlusRenewInfoEvent event = converter.toPlusRenewInfoEvent(dto,
                JuziPlusEnum.XEYK_CARD.getCode(),
                xeyk.getPlusDetailInfo() == null ? null : xeyk.getPlusDetailInfo().getOrderSn());
        PlusRenewInfoEntity renewInfo = orderQueryModel.getRenewInfo(event);
        if (renewInfo == null) {
            log.info("处理小额月卡入口,获取续费信息为空：{}", dto.getUserId());
            return;
        }
        // 续费状态为连续包月中
        if (renewInfo.getRenewState() != null && Objects.equals(renewInfo.getRenewState(),
                RenewStateEnum.TO_BE_RENEW.getCode())) {
            commonInfoVo.setXeykCancelRenew(true);
            commonInfoVo.setXeykCurrentOrderState(renewInfo.getCurrentOrderState());
        }
    }

    /**
     * 处理用户会员信息
     *
     * <AUTHOR>
     * @date 2022/11/09 11:05
     */
    private ProfitPlusDetailInfoEntity getPlusDetailInfo(ProfitsDetailReq req) {
        Integer userId = req.getUserId();
        Integer channelId = req.getChannelId();
        Integer configId = req.getConfigId();
        MemberPlusInfoEntity entity = new MemberPlusInfoEntity();
        //1、判断会员状态
        entity.setUserId(userId);
        entity.setChannelId(channelId);
        entity.setConfigId(configId);
        // 20230922 zjf 小额月卡可能不在当前周期，需要单独处理
        MemberPlusInfoDetailEntity memberPlusInfoDetailEntity;//kandaozhelile
        if (configId == JuziPlusEnum.XEYK_CARD.getCode()) {
            log.info("权益页-小额月卡会员身份查询：{}，{}", userId, configId);
            memberPlusInfoDetailEntity = plusQueryModel.getUserLastInfo(userId,
                    entity.getChannelId(), entity.getConfigId());
        } else {
            log.info("权益页-非小额月卡会员身份查询：{}，{}", userId, configId);
            memberPlusInfoDetailEntity = plusQueryModel.getCurrentMemberDetail(entity.getUserId(),
                    entity.getConfigId());
        }
        log.info("用户权益页-查询会员信息:{}", JSON.toJSONString(memberPlusInfoDetailEntity));
        if (memberPlusInfoDetailEntity == null || memberPlusInfoDetailEntity.getJxStatus() == null
                || !MemberPlusInfoStatusEnum.OPEN.getCode()
                .equals(memberPlusInfoDetailEntity.getJxStatus())) {
            log.info("用户权益页-会员状态不正确：userId：{}", userId);
            throw new PlusAbyssException(VipErrorEnum.PLUS_ERROR_300001);
        }
        //（3）、封装会员信息（设置周期信息，单号等）
        ProfitPlusDetailInfoEntity plusDetail = new ProfitPlusDetailInfoEntity();
        plusDetail.setOrderSn(memberPlusInfoDetailEntity.getOrderSn());
        plusDetail.setEndTime(
                LocalDateTimeUtils.parseDateToString(memberPlusInfoDetailEntity.getJxEndTime(),
                        LocalDateTimeUtils.DATE_FORMAT_SHORT_S));
        //当前周期开始时间
        Date stageStartTime = memberPlusInfoDetailEntity.getCreateTime();
        if (stageStartTime != null) {
            //权益时间开始时间+6天
            Date dateAfterDays = LocalDateTimeUtils.getDateAfterDays(stageStartTime, 6);
            plusDetail.setProfitEndTime(LocalDateTimeUtils.parseDateToString(dateAfterDays,
                    LocalDateTimeUtils.DATE_FORMAT_SHORT_S));
        }
        // huxf 2023.11.22 hello 增加当前会员后期开始结束时间
        plusDetail.setCurrentStartTime(memberPlusInfoDetailEntity.getJxStartTime());
        plusDetail.setCurrentEndTime(memberPlusInfoDetailEntity.getJxEndTime());
        //后支付订单-是否有后付款订单设置
        PlusOrderQueryReq query = new PlusOrderQueryReq();
        query.setUserId(entity.getUserId());
        query.setConfigId(entity.getConfigId());
        query.setPayType(PlusOrderPayTypeEnum.PAY_AFTER.getValue());
        query.setOrderState(PlusOrderStateEnum.WAIT_PAY.getCode());
        List<PlusOrderEntity> plusOrderInfoList = plusOrderRepository.getUserOrderList(query);
        log.info("用户权益页-查询后付款订单：userId：{}，info:{}", userId, JSON.toJSONString(plusOrderInfoList));
        if (!CollectionUtils.isEmpty(plusOrderInfoList)) {
            plusDetail.setOrderAfterPay(plusOrderInfoList.get(0).getOrderSn());
        }
        //设置方案id
        plusDetail.setProgramId(memberPlusInfoDetailEntity.getProgramId());
        log.info("用户权益页-处理用户会员信息结束：userId：{}，plusDetail:{}", userId, JSON.toJSONString(plusDetail));
        return plusDetail;
    }

    /**
     * 处理会员方案基本信息
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/6/14 14:54
     */
    private ProfitProgramBasicDetailEntity getProgramBasicInfo(ProfitsDetailReq req,
            ProfitPlusDetailInfoEntity detailInfo) {
        Integer userId = req.getUserId();
        Integer programId = detailInfo.getProgramId();
        log.info("用户权益页-查询关联方案信息：userId：{},programId:{}", userId, programId);
        PlusProgramEntity programEntity = programQueryModel.getById(programId);
        if (programEntity == null) {
            log.info("用户权益页-未获取到方案信息：userId：{}，programId：{}", userId, programId);
            throw new PlusAbyssException("未获取到会员方案");
        }
        ProfitProgramBasicDetailEntity basicInfo = converter.toProgramBasicDetailEntity(
                programEntity);
        basicInfo.setProgramId(programId);
        log.info("用户权益页-查询关联方案信息结束：userId：{},basicInfo:{}", userId, JSON.toJSONString(basicInfo));
        return basicInfo;
    }

    /**
     * 标识信息处理
     *
     * <AUTHOR>
     * @date 2022/11/09 11:15
     */
    private ProfitFlagDetailEntity getFlagInfo(ProfitsDetailReq req, ProfitDetailEntity entity) {
        try {
            ProfitFlagDetailEntity programFlag = new ProfitFlagDetailEntity();
            AuthApproval authApproval = authExternalRepository.getAuthState(req.getUserId(),
                    req.getChannelId());
            programFlag.setAuthState(authApproval.getAuthState());
            // 开通方式
            programFlag.setOpenModeList(
                    channelManagerQueryModel.getOpenModeList(req.getChannelId(), req.getConfigId(),
                            entity.getBasicInfo().getAfterPayState(), req.getUserId()));
            return programFlag;
        } catch (Exception e) {
            LogUtil.printLog("新权益页获取认证状态异常：userId：{}", req.getUserId(), e);
        }
        return null;
    }

    /**
     * 权益信息处理
     *
     * <AUTHOR>
     * @date 2022/11/09 11:17
     */
    private ProfitDetailBasicEntity getProfitInfo(ProfitsDetailReq req, ProfitDetailEntity entity,
            ProfitPlusDetailInfoEntity detailInfo) {
        Integer userId = req.getUserId();
        Integer programId = detailInfo.getProgramId();
        String orderSn = detailInfo.getOrderSn();
        log.info("用户权益页-权益信息处理处理开始：userId：{},programId:{}", userId, programId);
        ProfitDetailBasicEntity profitInfoVo = new ProfitDetailBasicEntity();
        //根据方案id查询关联的权益信息，按照顺序排列
        PlusOrderEntity orderEntity = new PlusOrderEntity();
        orderEntity.setOrderSn(orderSn);
        orderEntity.setConfigId(req.getConfigId());
        orderEntity.setProgramId(programId);
        List<PlusProModelEntity> rights = plusOrderSnapshtoQueryModel.listOrderModelByOrderBySort(
                orderEntity);
        profitInfoVo.setRightsNum(rights.size());
        List<ProfitModelBasicDetailEntity> resultBasic = new ArrayList<>();
        rights.forEach(proModel -> {
            HandleProfitQueryEvent queryEvent = converter.toHandleProfitQueryEvent(req,
                    proModel.getModelId(), programId, orderSn);
            //设置基本信息
            ProfitModelBasicDetailEntity basicInfoVo = handlerContext.getProfitBasicInfo(
                    queryEvent);
            if (basicInfoVo == null) {
                return;
            }
            // 20230513 zjf 非联名卡会员，权益页不展示联名卡权益
            if (Objects.equals(PlusModelEnum.LMQY.getModelId(), proModel.getModelId())) {
                PlusOrderExtInfoEntity extInfo = orderQueryModel.getOrderExtInfo(orderSn);
                if (extInfo == null || !BusinessTypeEnum.LMK.getCode()
                        .equals(extInfo.getBusinessType())) {
                    log.info("用户权益页-联名卡权益处理-非联名卡会员不展示联名卡权益：{}", orderSn);
                    return;
                }
            }
            //设置权益用户数据信息
            basicInfoVo.setData(handlerContext.getProfitForDetailOld(queryEvent));
            if (!CollectionUtils.isEmpty(basicInfoVo.getData())) {
                resultBasic.add(basicInfoVo);
            }
        });
        resultBasic.sort(Comparator.comparing(ProfitModelBasicDetailEntity::getSort));
        profitInfoVo.setModelBasicInfoVos(resultBasic);
        // 虚拟权益
        HandleProfitQueryEvent paramVo = new HandleProfitQueryEvent();
        paramVo.setProgramId(programId);
        paramVo.setUserId(userId);
        paramVo.setOrderSn(orderSn);
        List<ProfitVirtualProductTypeEntity> programVirtualVos = profitQueryModel.getProfitVirtualProductTypeEntityList(
                paramVo);
        profitInfoVo.setProgramVirtualVos(programVirtualVos);
        // 多级分类虚拟权益
        paramVo.setModelId(PlusModelEnum.QDSHQY.getModelId());
        List<ProfitVirtualProductTypeLevelEntity> programVirtualChannelVos = profitQueryModel.getProfitVirtualProductTypeLevelEntityList(
                paramVo, entity);
        profitInfoVo.setProgramVirtualChannelVos(programVirtualChannelVos);
        log.info("用户权益页-权益信息处理处理结束：userId：{},profitInfoVo:{}", userId,
                JSON.toJSONString(profitInfoVo));
        return profitInfoVo;
    }
}
