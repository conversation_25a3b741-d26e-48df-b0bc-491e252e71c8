package com.juzifenqi.plus.module.settle.repository.dao;

import com.juzifenqi.plus.dto.req.admin.settle.SettlePendingQueryReq;
import com.juzifenqi.plus.module.settle.model.contract.entity.PlusOrderSettleItemPendingAdminEntity;
import com.juzifenqi.plus.module.settle.repository.po.PlusOrderSettleItemPendingPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 会员订单待结算明细表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/3 15:46
 */
@Mapper
public interface IPlusOrderSettleItemPendingMapper {

    /**
     * 新增返回ID
     */
    Integer batchInsert(List<PlusOrderSettleItemPendingPo> list);

    /**
     * 按日期查询待结算的主体信息
     */
    List<PlusOrderSettleItemPendingPo> getSettlePendingSupplierByDate(
            @Param("beginTime") String beginTime, @Param("endTime") String endTime,
            @Param("isCreateSettle") Integer isCreateSettle);

    /**
     * 通过订单号查询待结算明细
     */
    List<PlusOrderSettleItemPendingPo> getSettlePendingItemsByOrderSn(
            @Param("orderSn") String orderSn);


    /**
     * 通过订单号和原支付流水号查询待结算明细
     */
    List<PlusOrderSettleItemPendingPo> getByOrderSnAndSerialNo(
            @Param("orderSn") String orderSn, @Param("serialNo") String serialNo);

    /**
     * 按日期和主体查询待结算明细
     */
    List<PlusOrderSettleItemPendingPo> getSettlePendingByDate(@Param("beginTime") String beginTime,
            @Param("endTime") String endTime, @Param("shuntSupplierId") Integer shuntSupplierId,
            @Param("separateSupplierId") Integer separateSupplierId,
            @Param("isCreateSettle") Integer isCreateSettle,
            @Param("channelId") Integer channelId);

    /**
     * 更新结算单id
     */
    Integer updateSettleItemPendingByIds(@Param("settleBillId") Long settleBillId,
            @Param("isCreateSettle") Integer isCreateSettle,
            @Param("allowCreateSettle") Integer allowCreateSettle, @Param("ids") List<Long> ids);

    /**
     * 分页查询待结算列表
     */
    List<PlusOrderSettleItemPendingAdminEntity> getPendingPageList(
            @Param("req") SettlePendingQueryReq req);

    /**
     * 查询待结算总条数
     */
    Integer pendingPageListCount(@Param("req") SettlePendingQueryReq req);
}
