package com.juzifenqi.plus.module.order.application;

import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.defray.PayDefrayResultEntity;
import com.juzifenqi.plus.module.order.model.event.order.CreateDefrayApplyEvent;
import com.juzifenqi.plus.module.order.model.event.order.OrderRefundSecondEvent;

/**
 * 订单代付
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 14:22
 */
public interface IPlusOrderDefrayApplication {

    /**
     * 创建代付申请
     */
    void createDefrayApply(CreateDefrayApplyEvent event);

    /**
     * 订单代付任务执行
     */
    void orderDefrayExecute();

    /**
     * 支付系统代付结果回调
     */
    void orderDefrayPayResult(PayDefrayResultEntity result);

    /**
     * 换卡代付退款处理
     */
    void changeCardDefrayPayRefund(OrderRefundNotifyEntity entity);

    /**
     * 代付发起第二次退款
     */
    void defrayPayRefundSecond(OrderRefundSecondEvent event);

    /**
     * 初始化代付新银行卡号密文
     */
    void initPastCardNoUuid(Long index, Integer size);

    /**
     * 检测代付新银行卡号密文
     */
    void inspectPastCardNoUuid(Integer size);
}
