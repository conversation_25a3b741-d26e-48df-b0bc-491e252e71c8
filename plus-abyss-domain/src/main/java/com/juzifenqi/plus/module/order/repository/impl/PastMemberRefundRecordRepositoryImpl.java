package com.juzifenqi.plus.module.order.repository.impl;

import com.juzifenqi.plus.module.common.converter.condtions.ShuntCondition;
import com.juzifenqi.plus.module.order.model.contract.IPastMemberRefundRecordRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PastMemberRefundRecordEntity;
import com.juzifenqi.plus.module.order.repository.converter.IPastMemberRefundRecordRepositoryConverter;
import com.juzifenqi.plus.module.order.repository.dao.IPastMemberRefundRecordMapper;
import com.juzifenqi.plus.module.order.repository.po.PastMemberRefundRecordPo;

import java.util.List;

import com.juzifenqi.product.util.StringUtil;
import com.juzishuke.dss.common.enums.DataTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * 代付打款
 *
 * <AUTHOR>
 */
@Repository
public class PastMemberRefundRecordRepositoryImpl implements IPastMemberRefundRecordRepository {

    @Autowired
    private IPastMemberRefundRecordMapper pastMemberRefundRecordMapper;
    @Autowired
    private ShuntCondition                shuntCondition;

    private final IPastMemberRefundRecordRepositoryConverter converter = IPastMemberRefundRecordRepositoryConverter.instance;

    /**
     * 保存代付打款任务
     */
    @Override
    public void save(PastMemberRefundRecordEntity pastMemberRefundRecord) {
        PastMemberRefundRecordPo pastMemberRefundRecordPo = converter.toPo(pastMemberRefundRecord);
        if (!StringUtil.isEmpty(pastMemberRefundRecordPo.getCardNo())) {
            if (shuntCondition.isUuid(pastMemberRefundRecord.getCardNo())) {
                pastMemberRefundRecordPo.setCardNoUuid(pastMemberRefundRecord.getCardNo());
                pastMemberRefundRecordPo.setCardNo(shuntCondition.uuidToDes(pastMemberRefundRecord.getCardNo(), DataTypeEnum.BANK_CARD));
            } else {
                pastMemberRefundRecordPo.setCardNoUuid(shuntCondition.desToUuid(pastMemberRefundRecordPo.getCardNo(), DataTypeEnum.BANK_CARD));
            }
        }
        pastMemberRefundRecordMapper.savePastMemberRefundRecord(pastMemberRefundRecordPo);
    }

    @Override
    public boolean hasIngEnd(String orderSn) {
        List<PastMemberRefundRecordPo> list = pastMemberRefundRecordMapper.loadByOrderSnAndState(
                orderSn);
        return !CollectionUtils.isEmpty(list);
    }

    @Override
    public List<PastMemberRefundRecordEntity> getToBeRefundList(List<Integer> businessTypes) {
        List<PastMemberRefundRecordPo> toBeRefundList = pastMemberRefundRecordMapper.getToBeRefundList(businessTypes);
        return converter.toPastMemberRefundRecordEntityList(toBeRefundList);
    }

    @Override
    public void batchProcessing(List<Integer> ids) {
        pastMemberRefundRecordMapper.batchProcessing(ids);
    }

    @Override
    public void updateState(PastMemberRefundRecordEntity entity) {
        PastMemberRefundRecordPo po = converter.toPo(entity);
        pastMemberRefundRecordMapper.updateRecord(po);
    }

    @Override
    public PastMemberRefundRecordEntity getById(Integer id) {
        PastMemberRefundRecordPo po = pastMemberRefundRecordMapper.loadPastMemberRefundRecord(id);
        return converter.toEntity(po);
    }

    @Override
    public PastMemberRefundRecordEntity getBySerialNumber(String serialNumber) {
        PastMemberRefundRecordPo po = pastMemberRefundRecordMapper.loadBySerialNumber(serialNumber);
        return converter.toEntity(po);
    }

    @Override
    public PastMemberRefundRecordEntity getByOrderSn(String orderSn) {
        PastMemberRefundRecordPo po = pastMemberRefundRecordMapper.loadByOrderSn(orderSn);
        return converter.toEntity(po);
    }

    @Override
    public PastMemberRefundRecordEntity loadPastMemberRefundRecord(Integer id) {
        return converter.toEntity(pastMemberRefundRecordMapper.loadPastMemberRefundRecord(id));
    }

    @Override
    public PastMemberRefundRecordEntity getByDefraySerialNo(String defraySerialNo) {
        PastMemberRefundRecordPo po = pastMemberRefundRecordMapper.getByDefraySerialNo(defraySerialNo);
        return converter.toEntity(po);
    }

    @Override
    public List<PastMemberRefundRecordEntity> getWithEmptyBankAccountNoUuid(Long lastId, Integer size) {
        List<PastMemberRefundRecordPo> toBeRefundList = pastMemberRefundRecordMapper.getWithEmptyBankAccountNoUuid(lastId,size);
        return converter.toPastMemberRefundRecordEntityList(toBeRefundList);
    }

    @Override
    public List<PastMemberRefundRecordEntity> getWithCardNoUuid(Long lastId, Integer size) {
        List<PastMemberRefundRecordPo> toBeRefundList = pastMemberRefundRecordMapper.getWithCardNoUuid(lastId,size);
        return converter.toPastMemberRefundRecordEntityList(toBeRefundList);
    }


    @Override
    public void updateBatchCardNoUuid(List<PastMemberRefundRecordEntity> refundRecordEntities) {
        pastMemberRefundRecordMapper.updateBatchCardNoUuid(converter.toPastMemberRefundRecordPoList(refundRecordEntities));
    }

}
