package com.juzifenqi.plus.module.order.model.converter;

import com.juzifenqi.plus.enums.PlusDeductLogTypeEnum;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberUseProductRecordEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.PlusProgramCashbackSnapshotEntity;
import com.juzifenqi.plus.module.asserts.repository.po.MemberUseProductRecordPo;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusDeductLogEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDeductPlanEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderRelationEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusProductOrderEntity;
import com.juzifenqi.plus.module.order.model.event.CreateDeductPlanEvent;
import com.juzifenqi.plus.module.order.model.event.HandleCommonDeductEvent;
import com.juzifenqi.plus.module.order.model.event.HandleTaskDeductFailEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductExcEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductFailEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductSuccessEvent;
import com.juzifenqi.plus.module.order.model.event.order.CreateOrderBillEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualGoodsOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderContractEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRelationCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusProductOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.UpdOrderStateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualOrderCreateEvent;
import com.juzifenqi.plus.module.order.repository.po.PlusRenewDetailPo;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo;
import com.juzifenqi.product.entity.Product;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface IPlusOrderModelConverter {

    IPlusOrderModelConverter instance = Mappers.getMapper(IPlusOrderModelConverter.class);

    @Mappings({@Mapping(target = "orderAmount", source = "plusOrderAmount"),
            @Mapping(target = "programId", source = "orderCreateEvent.programId"),
            @Mapping(target = "programPrice", source = "programEntity.mallMobilePrice"),
            @Mapping(target = "firstPayAmount", ignore = true),
            @Mapping(target = "discountRate", source = "discount")})
    PlusOrderEntity toOrderEntity(PlusOrderCreateEvent orderCreateEvent, BigDecimal plusOrderAmount,
            BigDecimal discount, PlusProgramEntity programEntity);

    @Mappings({@Mapping(target = "orderId", source = "plusOrderEntity.orderSn"),
            @Mapping(target = "customerId", source = "plusOrderEntity.userId"),
            @Mapping(target = "channelId", source = "plusOrderEntity.channelId"),
            @Mapping(target = "loanOrderAmount", source = "event.createOrderContext.loanAmount"),
            @Mapping(target = "loanOrderId", source = "event.createOrderContext.loanOrderSn")})
    PlusOrderContractEvent toPlusOrderContractEvent(PlusOrderEntity plusOrderEntity,
            PlusOrderCreateEvent event);


    @Mappings({@Mapping(target = "businessType", source = "relationBusinessType.code")})
    PlusOrderRelationEntity toPlusOrderRelationEntity(
            PlusOrderRelationCreateEvent plusOrderRelationCreateEvent);


    PlusOrderDeductPlanEntity toDeductPlanEntity(CreateDeductPlanEvent event);

    @Mappings({@Mapping(target = "userId", source = "createEvent.userId"),
            @Mapping(target = "channelId", source = "createEvent.channelId"),
            @Mapping(target = "configId", source = "order.configId"),
            @Mapping(target = "programId", source = "createEvent.programId"),
            @Mapping(target = "plusOrderSn", source = "order.orderSn"),
            @Mapping(target = "firstOrderSn", source = "createEvent.createOrderContext.firstOrderSn")})
    PlusProgramCashbackSnapshotEntity toplusProgramCashbackSnapshotEntity(
            PlusOrderCreateEvent createEvent, PlusOrderEntity order, Integer modelId);

    @Mappings({@Mapping(target = "userId", source = "param.userId"),
            @Mapping(target = "orderSn", source = "param.orderSn"),
            @Mapping(target = "plusOrderSn", source = "param.plusOrderSn"),
            @Mapping(target = "logType", source = "logType"),
            @Mapping(target = "logCode", source = "logTypeEnum.code"),
            @Mapping(target = "remark", source = "logTypeEnum.name")})
    PlusDeductLogEntity excToDeductLogEntity(PlusDeductExcEvent param, Integer logType,
            PlusDeductLogTypeEnum logTypeEnum);

    @Mappings({@Mapping(target = "userId", source = "param.plusOrderEntity.userId"),
            @Mapping(target = "orderSn", source = "param.loanOrderSn"),
            @Mapping(target = "plusOrderSn", source = "param.plusOrderEntity.orderSn"),
            @Mapping(target = "logType", source = "logType"),
            @Mapping(target = "logCode", source = "logTypeEnum.code"),
            @Mapping(target = "remark", source = "logTypeEnum.name"),
            @Mapping(target = "bankId", source = "param.bankId")})
    PlusDeductLogEntity commToDeductLogEntity(HandleCommonDeductEvent param, Integer logType,
            PlusDeductLogTypeEnum logTypeEnum);


    @Mappings({@Mapping(target = "userId", source = "param.userId"),
            @Mapping(target = "orderSn", source = "param.orderSn"),
            @Mapping(target = "plusOrderSn", source = "param.plusOrderSn"),
            @Mapping(target = "logType", source = "logType"),
            @Mapping(target = "logCode", source = "logTypeEnum.code"),
            @Mapping(target = "remark", source = "logTypeEnum.name"),
            @Mapping(target = "bankId", source = "param.bankId")})
    PlusDeductLogEntity failToDeductLogEntity(HandleTaskDeductFailEvent param, Integer logType,
            PlusDeductLogTypeEnum logTypeEnum);

    @Mappings({@Mapping(target = "userId", source = "event.plusOrderEntity.userId"),
            @Mapping(target = "plusOrderSn", source = "event.plusOrderEntity.orderSn"),
            @Mapping(target = "callBackEvent", source = "event.resEntity.callBackEvent"),
            @Mapping(target = "deductRespEvent", source = "event.resEntity.deductRespEvent"),
            @Mapping(target = "orderSn", source = "event.orderSn"),
            @Mapping(target = "configId", source = "event.plusOrderEntity.configId"),
            @Mapping(target = "channelId", source = "event.plusOrderEntity.channelId"),
            @Mapping(target = "bankId", source = "event.bankId")})
    HandleTaskDeductFailEvent toHandlerTaskDeductFailEvent(PlusDeductFailEvent event);


    HandleTaskDeductFailEvent toHandlerTaskDeductFailEvent(PlusDeductExcEvent deductExcEvent);

    @Mappings({@Mapping(target = "userId", source = "event.userId"),
            @Mapping(target = "channelId", source = "event.channelId"),
            @Mapping(target = "plusOrderSn", source = "event.orderSn"),
            @Mapping(target = "orderSn", source = "productOrderSn"),
            @Mapping(target = "modelId", source = "po.modelId"),
            @Mapping(target = "typeId", source = "po.typeId"),
            @Mapping(target = "programId", source = "po.programId"),
            @Mapping(target = "configId", source = "po.configId"),
            @Mapping(target = "productId", source = "po.productId"),
            @Mapping(target = "productSku", source = "event.productSku"),
            @Mapping(target = "productName", source = "product.name1"),
            @Mapping(target = "orderMoney", source = "orderAmount"),
            @Mapping(target = "orderTime", source = "orderTime"),
            @Mapping(target = "productSpu", source = "product.productCode"),
            @Mapping(target = "id", ignore = true), @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true)})
    MemberUseProductRecordPo toMemberUseProductRecordPo(PlusProductOrderCreateEvent event,
            PlusProgramProductNewPo po, BigDecimal orderAmount, Date orderTime,
            String productOrderSn, Product product);

    @Mappings({@Mapping(target = "plusOrderSn", source = "event.orderSn"),
            @Mapping(target = "cancelType", source = "cancelType"),
            @Mapping(target = "cancelReason", source = "event.changeReasonCode"),
            @Mapping(target = "channelId", source = "order.channelId"),
            @Mapping(target = "optUserId", source = "event.optUserId"),
            @Mapping(target = "optUserName", source = "event.optUserName"),
            @Mapping(target = "userId", source = "order.userId"),
            @Mapping(target = "configId", source = "order.configId"),
            @Mapping(target = "programId", source = "order.programId"),
            @Mapping(target = "saveNoticeTask", source = "notice")})
    PlusOrderCancelEvent toPlusOrderCancelEvent(UpdOrderStateEvent event, PlusOrderEntity order,
            Integer cancelType, boolean notice);

    @Mappings({@Mapping(target = "plusOrderSn", source = "entity.orderSn")})
    PlusOrderCancelEvent toPlusOrderCancelEvent(MemberPlusInfoDetailEntity entity,
            Integer cancelReason, Integer optUserId, String optUserName, boolean saveNoticeTask,
            String remark, Integer cancelType);

    @Mappings({@Mapping(target = "plusOrderSn", source = "entity.orderSn")})
    PlusOrderCancelEvent toPlusOrderCancelEvent(PlusOrderEntity entity, Integer cancelReason,
            Integer optUserId, String optUserName, boolean saveNoticeTask, String remark,
            Integer cancelType);

    @Mappings({@Mapping(target = "plusOrderSn", source = "entity.orderSn")})
    PlusOrderCancelEvent toPlusOrderCancelEvent(PlusOrderEntity entity, Integer cancelReason,
            Integer optUserId, String optUserName, boolean saveNoticeTask, String remark,
            Integer cancelType,String loanOrderSn);

    @Mappings({@Mapping(target = "plusOrderSn", source = "order.orderSn"),
            @Mapping(target = "remark", ignore = true)})
    PlusOrderCancelEvent toPlusOrderCancelEvent(PlusOrderEntity order);

    @Mappings({@Mapping(target = "id", ignore = true),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true),
            @Mapping(target = "channel", source = "order.channelId")})
    PlusRenewDetailPo toPlusRenewDetailPo(PlusOrderEntity order, Integer infoId,
            String serialNumber);

    @Mappings({@Mapping(target = "loanOrderSn", source = "event.orderSn")})
    HandleCommonDeductEvent toHandleCommonDeductEvent(PlusDeductFailEvent event);

    @Mappings({@Mapping(target = "loanOrderSn", source = "event.orderSn")})
    HandleCommonDeductEvent toHandleCommonDeductEvent(PlusDeductSuccessEvent event);

    @Mappings({@Mapping(target = "loanOrderSn", source = "event.orderSn")})
    HandleCommonDeductEvent toHandleCommonDeductEvent(PlusDeductExcEvent event);

    @Mapping(target = "plusOrderSn", source = "orderSn")
    CreateOrderBillEvent toCreateOrderBillEvent(PlusOrderEntity order);

    PlusDeductFailEvent toPlusDeductFailEvent(PlusDeductExcEvent event);

    List<PlusProductOrderEntity> toPlusProductOrderEntityList(
            List<MemberUseProductRecordEntity> list);

    VirtualOrderCreateEvent toVirtualOrderCreateEvent(VirtualGoodsOrderCreateEvent createEvent);
}
