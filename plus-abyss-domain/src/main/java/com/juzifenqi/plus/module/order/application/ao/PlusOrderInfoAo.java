package com.juzifenqi.plus.module.order.application.ao;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;


/**
 * 会员订单列表返回结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/20 10:09
 */
@Data
public class PlusOrderInfoAo implements Serializable {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单类型 1-开通2-续费3-升级 4-创建订单
     */
    private Integer orderType;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 方案名称
     */
    private String programName;

    /**
     * 订单状态 1_待支付;2_支付成功;3_取消
     */
    private Integer orderState;

    /**
     * 取消类型 1_有条件取消订单;2_无条件取消订单 3_急速退款-取消会员 4_过期未支付-系统取消 5_重复支付取消 6 按比例取消
     */
    private Integer cancelType;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 方案价格
     */
    private BigDecimal programPrice;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 订单回调时间
     */
    private Date callTime;

    /**
     * 支付成功时间
     */
    private Date payTime;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 支付方式 1-全款支付2-划扣3-后付款
     */
    private Integer payType;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 订单周期开始时间
     */
    private Date startTime;

    /**
     * 订单周期结束时间
     */
    private Date endTime;

    /**
     * 取消操作人
     */
    private String cancelOptName;

    /**
     * 会员状态  1生效  0过期  2失效 -1未知（前端展示空）,
     */
    private Integer plusState;

    /**
     * 业务类型 BusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 联名卡会员费
     */
    private BigDecimal lmkPlusAmount;

    /**
     * 订单标识 1=会员合并后的新订单
     */
    private Integer orderFlag;

    /**
     * 渠道标识 1-宜口贷 2-桔多多
     */
    private Integer bizSource;

}
