package com.juzifenqi.plus.module.order.repository.converter;

import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundDetailPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundInfoPo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2024/9/2 11:15
 */
@Mapper
public interface IPlusOrderRefundInfoRepositoryConverter {

    IPlusOrderRefundInfoRepositoryConverter instance = Mappers.getMapper(
            IPlusOrderRefundInfoRepositoryConverter.class);

    PlusOrderRefundInfoPo toPlusOrderRefundInfoPo(PlusOrderRefundInfoEntity entity);

    List<PlusOrderRefundInfoEntity> toPlusOrderRefundInfoEntityList(
            List<PlusOrderRefundInfoPo> list);

    PlusOrderRefundInfoEntity toPlusOrderRefundInfoEntity(PlusOrderRefundInfoPo byRefundSerialNo);


    PlusOrderRefundDetailPo toPlusOrderRefundDetailPo(PlusOrderRefundDetailEntity plusOrderRefundDetailEntity);

    List<PlusOrderRefundDetailPo> toPlusOrderRefundDetailPoList(List<PlusOrderRefundDetailEntity> list);


    PlusOrderRefundDetailEntity toPlusOrderRefundDetailEntity(PlusOrderRefundDetailPo byRefundSerialNo);

    List<PlusOrderRefundDetailEntity> toPlusOrderRefundDetailEntityList(List<PlusOrderRefundDetailPo> list);
}
