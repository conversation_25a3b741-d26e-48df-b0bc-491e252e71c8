package com.juzifenqi.plus.module.order.model;

import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.defray.PayDefrayResultEntity;
import com.juzifenqi.plus.module.order.model.event.order.CreateDefrayApplyEvent;
import com.juzifenqi.plus.module.order.model.event.order.OrderRefundSecondEvent;

/**
 * 订单代付
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 14:27
 */
public interface IPlusOrderDefrayModel {

    /**
     * 创建代付申请
     */
    void createDefrayApply(CreateDefrayApplyEvent event, PlusOrderEntity plusOrder);

    /**
     * 订单代付任务执行
     */
    void orderDefrayExecute();

    /**
     * 支付系统代付打款结果回调
     */
    void orderDefrayPayResult(PayDefrayResultEntity result);

    /**
     * 是否有待处理、处理中、打款成功、仅取消订单未打款的打款任务
     */
    boolean hasIngEnd(String orderSn);

    /**
     * 换卡代付退款处理
     */
    void changeCardDefrayPayRefund(OrderRefundNotifyEntity entity);


    void defrayPayRefundSecond(OrderRefundSecondEvent event);

    /**
     * 初始化代付新银行卡号密文
     */
    void initPastCardNoUuid(Long index, Integer size);

    /**
     * 检测代付新银行卡号密文
     * @param size
     */
    void inspectPastCardNoUuid(Integer size);

}
