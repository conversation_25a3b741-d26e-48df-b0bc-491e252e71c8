package com.juzifenqi.plus.module.market.api.converter;

import com.juzifenqi.plus.dto.req.market.PlusBillListMarketReq;
import com.juzifenqi.plus.dto.req.market.PlusLeadPageMarketReq;
import com.juzifenqi.plus.dto.req.market.PlusLoanConfirmMarketReq;
import com.juzifenqi.plus.dto.req.market.PlusLoanConfirmRdzxMarketReq;
import com.juzifenqi.plus.dto.req.market.PlusLoanMarketReq;
import com.juzifenqi.plus.dto.req.market.PlusOrderListMarketReq;
import com.juzifenqi.plus.dto.req.market.PlusPaySuccessMarketReq;
import com.juzifenqi.plus.dto.resp.detail.land.LandOldDetailResp;
import com.juzifenqi.plus.dto.resp.market.PlusBillListMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusLeadPageMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusLoanConfirmMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusLoanConfirmRdzxMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusLoanMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusOrderListMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusPaySuccessMarketResp;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.module.market.model.contract.entity.PlusBillListMarketEntity;
import com.juzifenqi.plus.module.market.model.contract.entity.PlusLeadPageMarketEntity;
import com.juzifenqi.plus.module.market.model.contract.entity.PlusLoanConfirmMarketEntity;
import com.juzifenqi.plus.module.market.model.contract.entity.PlusLoanConfirmRdzxMarketEntity;
import com.juzifenqi.plus.module.market.model.contract.entity.PlusLoanMarketEntity;
import com.juzifenqi.plus.module.market.model.contract.entity.PlusOrderListMarketEntity;
import com.juzifenqi.plus.module.market.model.contract.entity.PlusPaySuccessMarketEntity;
import com.juzifenqi.plus.module.market.model.event.PlusBillListMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusLeadPageMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusLoanConfirmMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusLoanConfirmRdzxMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusLoanMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusOrderListMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusPaySuccessMarketEvent;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandOldDetailEntity;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 会员营销转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/23 10:55
 */
@Mapper
public interface IPlusMarketApiConverter {

    IPlusMarketApiConverter instance = Mappers.getMapper(IPlusMarketApiConverter.class);

    PlusPaySuccessMarketEvent toPlusPaySuccessMarketEvent(PlusPaySuccessMarketReq req);

    PlusPaySuccessMarketResp toPlusPaySuccessMarketResp(PlusPaySuccessMarketEntity entity);

    PlusOrderListMarketEvent toPlusOrderListMarketEvent(PlusOrderListMarketReq req);

    PlusOrderListMarketResp toPlusOrderListMarketResp(PlusOrderListMarketEntity entity);

    PlusBillListMarketEvent toPlusBillListMarketEvent(PlusBillListMarketReq req);

    PlusBillListMarketResp toPlusBillListMarketResp(PlusBillListMarketEntity entity);

    PlusLoanConfirmRdzxMarketEvent toPlusLoanConfirmMarketEvent(PlusLoanConfirmRdzxMarketReq req);

    PlusLoanConfirmRdzxMarketResp toPlusLoanConfirmMarketResp(
            PlusLoanConfirmRdzxMarketEntity entity);

    PlusLeadPageMarketEvent toPlusLeadPageMarketEvent(PlusLeadPageMarketReq req);

    PlusLeadPageMarketResp toPlusLeadPageMarketResp(PlusLeadPageMarketEntity entity);

    PlusLoanConfirmMarketEvent toPlusLoanConfirmMarketEvent(PlusLoanConfirmMarketReq req);

    PlusLoanConfirmMarketResp toLoanConfirmMarketResp(PlusLoanConfirmMarketEntity entity);

    PlusLoanMarketEvent toPlusLoanMarketEvent(PlusLoanMarketReq req);

    PlusLoanMarketResp toPlusLoanMarketResp(PlusLoanMarketEntity entity);

    @AfterMapping
    default void ofPlusLoanMarketResp(@MappingTarget PlusLoanMarketResp resp, PlusLoanMarketEntity entity) {
        if (null != entity && !CollectionUtils.isEmpty(entity.getPayTypes())) {
            if (entity.getPayTypes().contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                //计算剩余支付金额
                if (null != entity.getMallMobilePrice() && null != entity.getFirstPayAmount()) {
                    if ( null != entity.getDiscountRate() ) {
                        //有折扣
                        //首付剩余支付金额
                        BigDecimal surplusPayAmount = entity.getMallMobilePrice().multiply(entity.getDiscountRate()).setScale(0, RoundingMode.FLOOR).subtract(entity.getFirstPayAmount());
                        resp.setSurplusPayAmount(surplusPayAmount);
                    } else {
                        //无折扣
                        BigDecimal surplusPayAmount = entity.getMallMobilePrice().subtract(entity.getFirstPayAmount());
                        resp.setSurplusPayAmount(surplusPayAmount);
                    }
                }
            }
        }
    }

    @AfterMapping
    default void  ofPlusPaySuccessMarketResp(@MappingTarget PlusPaySuccessMarketResp resp,PlusPaySuccessMarketEntity entity) {
        if (null != entity && !CollectionUtils.isEmpty(entity.getPayTypes())) {
            if (entity.getPayTypes().contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                //计算剩余支付金额
                if (null != entity.getMallMobilePrice() && null != entity.getFirstPayAmount()) {
                    //无折扣
                    BigDecimal surplusPayAmount = entity.getMallMobilePrice().subtract(entity.getFirstPayAmount());
                    resp.setSurplusPayAmount(surplusPayAmount);
                }
            }
        }
    }

    @AfterMapping
    default void ofPlusOrderListMarketResp(@MappingTarget PlusOrderListMarketResp resp , PlusOrderListMarketEntity entity) {
        if (null != entity && !CollectionUtils.isEmpty(entity.getPayTypes())) {
            if (entity.getPayTypes().contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                //计算剩余支付金额
                if (null != entity.getMallMobilePrice() && null != entity.getFirstPayAmount()) {
                    if ( null != entity.getDiscountRate() ) {
                        //有折扣
                        //首付剩余支付金额
                        BigDecimal surplusPayAmount = entity.getMallMobilePrice().multiply(entity.getDiscountRate()).subtract(entity.getFirstPayAmount());
                        resp.setSurplusPayAmount(surplusPayAmount);
                    } else {
                        //无折扣
                        BigDecimal surplusPayAmount = entity.getMallMobilePrice().subtract(entity.getFirstPayAmount());
                        resp.setSurplusPayAmount(surplusPayAmount);
                    }
                }
            }
        }
    }

    @AfterMapping
    default void   ofPlusBillListMarketResp(@MappingTarget PlusBillListMarketResp resp,PlusBillListMarketEntity entity) {
        if (null != entity && !CollectionUtils.isEmpty(entity.getPayTypes())) {
            if (entity.getPayTypes().contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                //计算剩余支付金额
                if (null != entity.getMallMobilePrice() && null != entity.getFirstPayAmount()) {
                    //无折扣
                    BigDecimal surplusPayAmount = entity.getMallMobilePrice().subtract(entity.getFirstPayAmount());
                    resp.setSurplusPayAmount(surplusPayAmount);
                }
            }
        }
    }

    @AfterMapping
    default void  ofPlusLoanConfirmMarketResp(@MappingTarget PlusLoanConfirmMarketResp resp, PlusLoanConfirmMarketEntity entity) {
        if (null != entity && !CollectionUtils.isEmpty(entity.getPayTypes())) {
            if (entity.getPayTypes().contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                //计算剩余支付金额
                if (null != entity.getMallMobilePrice() && null != entity.getFirstPayAmount()) {
                    if ( null != entity.getDiscountRate() ) {
                        //有折扣
                        //首付剩余支付金额
                        BigDecimal surplusPayAmount = entity.getMallMobilePrice().multiply(entity.getDiscountRate()).setScale(0, RoundingMode.FLOOR).subtract(entity.getFirstPayAmount());
                        resp.setSurplusPayAmount(surplusPayAmount);
                    } else {
                        //无折扣
                        BigDecimal surplusPayAmount = entity.getMallMobilePrice().subtract(entity.getFirstPayAmount());
                        resp.setSurplusPayAmount(surplusPayAmount);
                    }
                }
            }
        }
    }
}
