package com.juzifenqi.plus.module.common.repository.external.acl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.external.AcmExternal;
import com.juzifenqi.plus.module.common.IAcmExternalRepository;
import java.util.List;

import com.juzishuke.framework.common.response.BaseResponse;
import com.yikoudai.zijin.cove.open.bill.req.PlanQueryReq;
import com.yikoudai.zijin.cove.open.bill.req.PlanSummaryReq;
import com.yikoudai.zijin.cove.open.bill.resp.PlanSummaryResp;
import com.yikoudai.zijin.cove.open.info.RepayPlanInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 核心
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 13:56
 */
@Service
@Slf4j
public class AcmExternalRepositoryAcl implements IAcmExternalRepository {

    @Autowired
    private AcmExternal acmExternal;

    @Override
    public void invalidRepayCoupon(String couponNo) {
//        log.info("调用核心失效还款券开始：{}", couponNo);
//        JsonResult<List<String>> result = acmExternal.disableCoupon(couponNo);
//        log.info("调用核心失效还款券返回：{}", JSON.toJSONString(result));
    }

    @Override
    public List<RepayPlanInfo> getRepayPlan(PlanQueryReq req) {
        try {
            log.info("拉取还款计划入参：{}", JSON.toJSONString(req));
            List<RepayPlanInfo> repayPlan = acmExternal.getRepayPlan(req);
            log.info("拉取还款计划返回：{}", JSON.toJSONString(repayPlan));
            return repayPlan;
        } catch (Exception e) {
            LogUtil.printLog(e, "拉取还款计划异常");
        }
        return null;
    }

    @Override
    public PlanSummaryResp queryRepayPlanSummary(Integer channelId, Integer userId) {
        PlanSummaryReq planSummaryReq = new PlanSummaryReq();
        planSummaryReq.setApplication(String.valueOf(channelId));
        planSummaryReq.setCustomerIds(Lists.newArrayList(userId));
        return acmExternal.queryRepayPlanSummary(planSummaryReq);
    }


}
