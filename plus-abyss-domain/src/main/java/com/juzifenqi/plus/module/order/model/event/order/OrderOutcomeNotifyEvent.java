package com.juzifenqi.plus.module.order.model.event.order;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 出账通知
* <AUTHOR>
* @version 1.0
* @date 2023/12/19 10:09
*/
@Data
public class OrderOutcomeNotifyEvent {

    /**
     * 会员订单号
     */
    private String plusOrderSn;

    /**
     * 支付流水号
     */
    private String serialNo;

    /**
     * 退款金额,单位:元
     */
    private BigDecimal refundAmount;

    /**
     * 退款方式，1=代付 2=原路退
     */
    private Integer refundType;

    /**
     * 退款状态，1_退款成功 2_退款失败
     */
    private Integer refundState;
}
