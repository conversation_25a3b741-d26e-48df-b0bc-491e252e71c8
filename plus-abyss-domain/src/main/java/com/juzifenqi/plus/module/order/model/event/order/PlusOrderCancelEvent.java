package com.juzifenqi.plus.module.order.model.event.order;

import com.juzifenqi.plus.enums.CancelReasonEnum;
import com.juzifenqi.plus.enums.PlusCancelTypeEnum;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PlusOrderCancelEvent {

    /**
     * 订单号
     */
    private String plusOrderSn;

    /**
     * 取消类型：有条件、无条件、急速、延迟、未过期、过期、按比例
     *
     * @see PlusCancelTypeEnum
     */
    private Integer cancelType;

    /**
     * 取消原因code
     *
     * @see CancelReasonEnum
     */
    private Integer cancelReason;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 操作人姓名
     */
    private String optUserName;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 订单中心订单号
     */
    private String loanOrderSn;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 已用权益类型逗号分隔
     */
    private String useProfit;

    /**
     * 是否需要保存回调三方的任务
     * <p>默认为true，PlusOrderRefundApplyModelImpl#orderRefund过来的请求不需要再保存回调任务</p>
     */
    private boolean saveNoticeTask = true;

    /**
     * 退款比例
     */
    private BigDecimal refundRate;

    /**
     * 是否批量取消
     */
    private boolean batchOpt;

    /**
     * 是否需要获取人脸识别结果
     */
    private boolean needUserFaceAuth;

    /**
     * 是否回调通知,用于判断是否处理订单取消前的校验
     */
    private boolean notify;

    /**
     * 退款金额,单位:元
     */
    private BigDecimal refundOrderAmount;

    /**
     * 扩展信息，透传给订单，订单透传给支付，支付回调带过来
     * <p>整体json长度不能超过255个</p>
     */
    private OrderCancelExtInfoEvent extInfo;


    private String outOrderSn;
    /**
     * 渠道标识
     */
    private Integer bizSource;


    private Integer optUserType;

}
