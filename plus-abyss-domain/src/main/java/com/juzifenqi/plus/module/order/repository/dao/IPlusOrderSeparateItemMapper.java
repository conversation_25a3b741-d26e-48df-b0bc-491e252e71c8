package com.juzifenqi.plus.module.order.repository.dao;

import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemAdminEntity;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparateItemPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 会员订单分账记录明细
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/31 11:09
 */
@Mapper
public interface IPlusOrderSeparateItemMapper {

    /**
     * 新增返回ID
     */
    Integer savePlusOrderSeparateItem(PlusOrderSeparateItemPo po);

    /**
     * 更新
     */
    Integer updatePlusOrderSeparateItem(@Param("plusOrderSeparateItem") PlusOrderSeparateItemPo po);

    /**
     * 新增返回ID
     */
    Integer batchInsert(List<PlusOrderSeparateItemPo> list);

    /**
     * 查询分账明细
     */
    List<PlusOrderSeparateItemPo> getPlusOrderSeparateItems(PlusOrderSeparateItemPo po);

    /**
     * 查询订单清分明细
     */
    List<PlusOrderSeparateItemAdminEntity> getOrderSeparateItemList(
            @Param("orderSn") String orderSn,
            @Param("separateEnableState") Integer separateEnableState);

    /**
     * 根据订单号和支付动作查询分账明细
     */
    List<PlusOrderSeparateItemPo> getSeparateItemsByOrderSnAndPayAction(
            @Param("orderSn") String orderSn,
            @Param("orderPayAction") Integer orderPayAction);
}
