package com.juzifenqi.plus.module.order.api.converter;

import com.juzifenqi.plus.dto.resp.admin.*;
import com.juzifenqi.plus.module.common.entity.MemberPlusSystemLogEntity;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderDetailAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderInfoAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderSeparateAo;
import com.juzifenqi.plus.module.order.application.ao.PlusRefundAo;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusProductOrderEntity;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 浩瀚后台订单转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/6 16:58
 */
@Mapper
public interface IPlusOrderAdminApiConverter {

    IPlusOrderAdminApiConverter instance = Mappers.getMapper(IPlusOrderAdminApiConverter.class);

    List<PlusOrderInfoResp> toPlusOrderInfoRespList(List<PlusOrderInfoAo> aoList);

    @Mapping(source = "separateList", target = "payRecordList")
    PlusOrderDetailResp toPlusOrderDetailResp(PlusOrderDetailAo detailAo);

    List<PlusOrderSeparateResp> toPlusOrderSeparateRespList(List<PlusOrderSeparateAo> aoList);

    List<MemberPlusSystemLogResp> toMemberPlusSystemLogRespList(
            List<MemberPlusSystemLogEntity> entityList);

    List<PlusProductOrderResp> toPlusProductOrderRespList(List<PlusProductOrderEntity> list);

    PlusRefundAoResp toPlusRefundAoResp(PlusRefundAo plusRefundAo);

    List<PlusRefundAoResp> toPlusRefundAoRespList(List<PlusRefundAo> plusRefundAoList);
}
