package com.juzifenqi.plus.module.order.repository.converter;

import com.juzifenqi.plus.module.market.model.contract.entity.LoanSuccessOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PastMemberRefundRecordEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderExtInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderResubmitFlagEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderShuntEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.bill.PlusOrderBillEntity;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderContractEvent;
import com.juzifenqi.plus.module.order.repository.converter.condtions.IPlusOrderConverterCondition;
import com.juzifenqi.plus.module.order.repository.po.LoanSuccessOrderPo;
import com.juzifenqi.plus.module.order.repository.po.PastMemberRefundRecordPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderExtInfoPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderResubmitFlagPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderShuntPo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 会员订单对象转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/29 15:32
 */
@Mapper(uses = {IPlusOrderConverterCondition.class})
public interface IPlusOrderConverter {

    IPlusOrderConverter instance = Mappers.getMapper(IPlusOrderConverter.class);


    @Mappings({@Mapping(target = "userId", source = "orderInfoPo.userId"),
            @Mapping(target = "channelId", source = "orderInfoPo.channelId"),
            @Mapping(target = "orderSn", source = "orderInfoPo.orderSn"),
            @Mapping(target = "orderAmount", source = "orderInfoPo.orderAmount"),
            @Mapping(target = "createTime", source = "orderInfoPo.createTime"),
            @Mapping(target = "endTime", source = "orderInfoPo.endTime"),
            @Mapping(target = "updateTime", source = "orderInfoPo.updateTime"),
            @Mapping(target = "payType", source = "orderInfoPo.payType"),
            @Mapping(target = "lmkPlusAmount", source = "extInfoPo.orderAmount"),
            @Mapping(target = "firstPayAmount", source = "orderInfoPo.firstPayAmount")}
    )
    PlusOrderEntity toPlusOrderEntity(PlusOrderInfoPo orderInfoPo, PlusOrderExtInfoPo extInfoPo);

    PlusOrderEntity toPlusOrderEntity(PlusOrderInfoPo orderInfoPo);


    PlusOrderExtInfoEntity toPlusExtInfoEntity(PlusOrderExtInfoPo orderInfoPo);

    List<PlusOrderExtInfoEntity> toPlusExtInfoEntityList(List<PlusOrderExtInfoPo> orderInfoPos);


    List<PastMemberRefundRecordEntity> toRefundRecordEntity(
            List<PastMemberRefundRecordPo> refundRecordPos);

    List<PlusOrderEntity> toPlusOrderEntityList(List<PlusOrderInfoPo> orderInfoPoList);

    @Mappings({@Mapping(target = "customerId", source = "userId"),
            @Mapping(target = "orderId", source = "orderSn"),
            @Mapping(target = "contractNoList", source = "contractNo", qualifiedByName = "convertContractNo")})
    PlusOrderContractEvent toPlusOrderContractEvent(PlusOrderShuntPo shuntPo);

    @Mappings({@Mapping(target = "customerId", source = "shuntPo.userId"),
            @Mapping(target = "orderId", source = "shuntPo.orderSn"),
            @Mapping(target = "contractNoList", source = "contractNoList")})
    PlusOrderContractEvent toPlusOrderContractEvent(PlusOrderShuntPo shuntPo,
            List<String> contractNoList);


    @Mappings({@Mapping(target = "inSupplier", source = "po.planInSupplier"),
            @Mapping(target = "updateTime", ignore = true)})
    PlusOrderBillEntity toPlusOrderBillEntity(PlusOrderShuntPo po, Integer programId);

    PlusOrderShuntEntity toPlusOrderShuntEntity(PlusOrderShuntPo po);

    List<PlusOrderResubmitFlagEntity> toPlusOrderResubmitFlagEntityList(
            List<PlusOrderResubmitFlagPo> poList);

    List<LoanSuccessOrderEntity> toLoanSuccessOrderEntityList(List<LoanSuccessOrderPo> list);
}
