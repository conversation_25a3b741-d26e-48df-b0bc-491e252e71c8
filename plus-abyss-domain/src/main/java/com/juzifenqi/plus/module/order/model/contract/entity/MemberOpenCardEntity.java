package com.juzifenqi.plus.module.order.model.contract.entity;

import lombok.Data;

/**
 * 订单中心会员订单支付成功通知对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/18 14:49
 */
@Data
public class MemberOpenCardEntity {

    /**
     * 用户ID
     */
    public Integer memberId;

    /**
     * 会员方案
     */
    public Integer plusType;

    /**
     * 订单类型
     */
    public Integer orderType;

    /**
     * 订单号
     */
    public String orderSn;

    /**
     * 渠道号
     */
    public Integer channel;

    /**
     * 支付流水号
     */
    public String serialNumber;
    /**
     * 借款订单
     */
    public String orderId;

    /**
     * 订单状态
     */
    public Integer orderState;
}
