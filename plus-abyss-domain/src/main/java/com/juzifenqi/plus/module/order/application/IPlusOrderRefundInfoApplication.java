package com.juzifenqi.plus.module.order.application;

import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.order.model.event.order.OrderRefundSecondEvent;

/**
 * <AUTHOR>
 * @date 2024/9/7 14:27
 */
public interface IPlusOrderRefundInfoApplication {

    /**
     * 原路退款、原路换卡退款
     */
    void originalRefund(OrderRefundNotifyEntity entity);

    /**
     * 第二次退款
     */
    void secondRefund(OrderRefundSecondEvent event);

}
