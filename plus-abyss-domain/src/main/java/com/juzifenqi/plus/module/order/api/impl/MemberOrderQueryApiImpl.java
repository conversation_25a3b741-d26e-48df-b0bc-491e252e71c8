package com.juzifenqi.plus.module.order.api.impl;

import com.juzifenqi.plus.api.IMemberOrderQueryApi;
import com.juzifenqi.plus.dto.req.member.MemberQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOrderQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOutOrderQueryReq;
import com.juzifenqi.plus.dto.req.order.RdzxOrderCostQueryReq;
import com.juzifenqi.plus.dto.req.order.RdzxOrderQueryReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.admin.PlusOrderInfoResp;
import com.juzifenqi.plus.dto.resp.order.RdzxCostResp;
import com.juzifenqi.plus.dto.resp.order.RdzxOrderCostResp;
import com.juzifenqi.plus.dto.resp.order.RdzxOrdersResp;
import com.juzifenqi.plus.dubbo.DubboService;
import com.juzifenqi.plus.module.order.api.converter.IMemberOrderQueryApiConverter;
import com.juzifenqi.plus.module.order.application.IMemberOrderQueryApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderQueryApplication;
import com.juzifenqi.plus.module.order.model.contract.entity.query.RdzxCostEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.query.RdzxOrderCostEntity;
import java.util.List;
import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 用户订单相关查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/2 14:16
 */
@DubboService
public class MemberOrderQueryApiImpl implements IMemberOrderQueryApi {

    private final IMemberOrderQueryApiConverter converter = IMemberOrderQueryApiConverter.instance;

    @Autowired
    private IMemberOrderQueryApplication queryApplication;
    @Resource
    private IPlusOrderQueryApplication   orderQueryApplication;

    @Override
    public PlusAbyssResult<RdzxOrderCostResp> getRdzxOrderCost(RdzxOrderCostQueryReq req) {
        if (req == null || StringUtils.isBlank(req.getPlusOrderSn())) {
            return PlusAbyssResult.error("会员单号参数不能为空");
        }
        RdzxOrderCostEntity rdzxOrderCost = queryApplication.getRdzxOrderCost(req);
        return PlusAbyssResult.success(converter.toRdzxOrderCostResp(rdzxOrderCost));
    }

    @Override
    public PlusAbyssResult<RdzxCostResp> getRdzxCost(RdzxOrderCostQueryReq req) {
        if (req == null || req.getUserId() == null) {
            return PlusAbyssResult.error("用户Id参数不能为空");
        }
        RdzxCostEntity rdzxCost = queryApplication.getRdzxCost(req);
        return PlusAbyssResult.success(converter.toRdzxCostResp(rdzxCost));
    }

    @Override
    public PlusAbyssResult<RdzxOrdersResp> getRdzxOrder(RdzxOrderQueryReq req) {
        if (CollectionUtils.isEmpty(req.getLoanOrderList())) {
            return PlusAbyssResult.error("借款订单号列表不能为空");
        }
        List<RdzxOrderCostEntity> rdzxOrderList = queryApplication.getRdzxOrderList(req.getLoanOrderList());
        return PlusAbyssResult.success(new RdzxOrdersResp(converter.toRdzxOrderCostResp(rdzxOrderList)));
    }

    /**
     * 获取7天内购买的有效会员订单信息
     */
    @Override
    public PlusAbyssResult<List<PlusOrderInfoResp>> getMemberSevenOrderList(MemberQueryReq req) {
        return PlusAbyssResult.success(
                converter.toPlusOrderInfoRespList(queryApplication.getMemberSevenOrderList(req)));
    }

    /**
     * 根据条件查询用户订单列表
     */
    @Override
    public PlusAbyssResult<List<PlusOrderInfoResp>> getUserPlusOrderList(PlusOrderQueryReq req) {
        return PlusAbyssResult.success(
                converter.toPlusOrderInfoRespList(orderQueryApplication.getUserPlusOrderList(req)));
    }

    @Override
    public PlusAbyssResult<List<PlusOrderInfoResp>> getUserWaitPayOrderList(PlusOrderQueryReq req) {
        if (req.getUserId() == null || req.getUserId() == 0) {
            return PlusAbyssResult.error("userId参数不能为空");
        }
        if (req.getChannelId() == null || req.getChannelId() == 0) {
            return PlusAbyssResult.error("channelId参数不能为空");
        }
        return PlusAbyssResult.success(converter.toPlusOrderInfoRespList(
                orderQueryApplication.getUserWaitPayOrderList(req)));
    }

    @Override
    public PlusAbyssResult<PlusOrderInfoResp> getUserOutPlusOrder(PlusOutOrderQueryReq req) {
        if (req.getUserId() == null || req.getUserId() == 0) {
            return PlusAbyssResult.error("userId参数不能为空");
        }
        if (req.getChannelId() == null || req.getChannelId() == 0) {
            return PlusAbyssResult.error("channelId参数不能为空");
        }
        if (StringUtils.isBlank(req.getOrderSn()) && StringUtils.isBlank(req.getOutOrderSn())) {
            return PlusAbyssResult.error("orderSn和outOrderSn不能同时为空");
        }
        return PlusAbyssResult.success(converter.toPlusOrderInfoResp(
                orderQueryApplication.getUserOutPlusOrder(req)));
    }
}
