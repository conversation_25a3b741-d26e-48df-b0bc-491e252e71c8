package com.juzifenqi.plus.module.order.application.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.groot.utils.core.date.LocalDateTimeUtils;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.magic.bean.enums.BizSourceEnum;
import com.juzifenqi.oms.enums.OrderStateEnum;
import com.juzifenqi.order.vo.OrderCancelRefundResultVO;
import com.juzifenqi.order.vo.OrderCancelRefundVO;
import com.juzifenqi.order.vo.OrderExtraVO;
import com.juzifenqi.order.vo.RefundInfo;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.*;
import com.juzifenqi.plus.dto.pojo.CreateOrderContext;
import com.juzifenqi.plus.dto.req.admin.PlusOrderDetailQueryReq;
import com.juzifenqi.plus.dto.req.admin.PlusOrderInfoQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOrderQueryReq;
import com.juzifenqi.plus.enums.*;
import com.juzifenqi.plus.enums.pay.PayProductCodeEnum;
import com.juzifenqi.plus.enums.pay.PayStateCodeEnum;
import com.juzifenqi.plus.enums.refund.RefundInfoStateEnum;
import com.juzifenqi.plus.enums.supplier.SupplierTypeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.adapter.IMemberPlusProfitsAdapter;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusApplication;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusInfoApplication;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusSendPlanApplication;
import com.juzifenqi.plus.module.asserts.model.MemberProfitsQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusExclusiveRepository;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusInfoDetailRepository;
import com.juzifenqi.plus.module.asserts.model.contract.entity.*;
import com.juzifenqi.plus.module.asserts.model.event.*;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.PlusOrderCancelFinishEvent;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.PlusOrderPayFinishEvent;
import com.juzifenqi.plus.module.asserts.model.impl.strategy.profits.ProfitHandlerContext;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailExtPo;
import com.juzifenqi.plus.module.asserts.repository.po.PlusUseProfitSuPo;
import com.juzifenqi.plus.module.common.*;
import com.juzifenqi.plus.module.common.entity.MemberPlusSystemLogEntity;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.common.entity.PlusMemberBlackEntity;
import com.juzifenqi.plus.module.order.adapter.IPlusOrderAdapter;
import com.juzifenqi.plus.module.order.adapter.event.PlusRepeatPayEvent;
import com.juzifenqi.plus.module.order.application.*;
import com.juzifenqi.plus.module.order.application.ao.*;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderPayInfoAo.DivideInfo;
import com.juzifenqi.plus.module.order.application.converter.IPlusOrderApplicationConverter;
import com.juzifenqi.plus.module.order.application.impl.strategy.HandlerApplicationContext;
import com.juzifenqi.plus.module.order.application.validator.PlusOrderValidator;
import com.juzifenqi.plus.module.order.model.*;
import com.juzifenqi.plus.module.order.model.contract.IPastMemberRefundRecordRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.*;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.PayCallbackEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.pay.NewPayResultCallbackEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.repay.PlusRepayRenewEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IAlphaExternalRepository;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.model.event.*;
import com.juzifenqi.plus.module.order.model.event.notice.PlusOrderNoticeEvent;
import com.juzifenqi.plus.module.order.model.event.order.*;
import com.juzifenqi.plus.module.order.model.impl.factory.MemberPlusRenewPlanFactory;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.utils.*;
import com.yikoudai.zijin.cove.open.bill.resp.PlanSummaryResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.List;

import static com.juzifenqi.plus.constants.CommonConstant.RDZX_SUBJECT;
import static com.juzifenqi.plus.constants.CommonConstant.SUBJECT;
import static com.juzifenqi.plus.constants.RedisConstantPrefix.*;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class PlusOrderApplicationImpl implements IPlusOrderApplication {

    @Autowired
    private PlusOrderModel                  plusOrderModel;
    @Autowired
    private IMemberPlusInfoApplication      memberCardApplication;
    @Autowired
    private IPlusProgramQueryModel          programQueryModel;
    @Autowired
    private PlusOrderQueryModel             plusOrderQueryModel;
    @Autowired
    private PlusOrderValidator              plusOrderValidator;
    @Autowired
    private MemberPlusRenewPlanQueryModel   renewPlanQueryModel;
    @Autowired
    private IMemberPlusRenewPlanApplication renewPlanApplication;
    @Autowired
    private MemberPlusRenewPlanFactory      memberPlusRenewPlanFactory;
    @Autowired
    private IIMRepository                   imRepository;
    @Autowired
    private IPlusOrderAdapter               plusOrderAdapter;
    @Autowired
    private HandlerApplicationContext       handlerApplicationContext;
    @Autowired
    private RedisUtils                      redisUtils;
    @Autowired
    private PlusOrderDeductPlanModel        plusOrderDeductPlanModel;
    @Autowired
    private ConfigProperties                configProperties;
    @Autowired
    private IMemberPlusSendPlanApplication  memberPlusSendPlanApplication;
    @Autowired
    private IOrderExternalRepository        iOrderExternalRepository;
    @Autowired
    private IMemberPlusProfitsAdapter       memberPlusVirtualAdapter;
    @Autowired
    private MemberProfitsQueryModel         profitsQueryModel;
    @Autowired
    private IAlphaExternalRepository        alphaExternalRepository;
    @Autowired
    private IMemberPlusApplication          memberPlusApplication;
    @Autowired
    private IPlusOrderNoticeApplication     noticeApplication;
    @Autowired
    private IWorkOrderExternalRepository    workOrderExternalRepository;
    @Autowired
    private IPlusOrderAdapter               orderAdapter;
    @Autowired
    private IPlusRepayModel                 repayModel;
    @Autowired
    private RedisLock                       redisLock;
    @Autowired
    private IPlusOrderShuntModel            orderShuntModel;
    @Autowired
    private IPlusOrderBillModel             orderBillModel;
    @Autowired
    private IPlusPayRecordModel             payRecordModel;
    @Autowired
    private IMemberPlusSystemLogRepository  logRepository;
    @Autowired
    private PlusOrderDeductPlanModel        deductPlanModel;
    @Autowired
    private IMemberPlusSendPlanApplication  profitsSendPlanApplication;
    @Autowired
    private IPlusOrderRefundInfoModel       orderRefundInfoModel;
    @Autowired
    private SpringUtils                     springUtils;
    @Autowired
    private PlusOrderSeparateModel          separateModel;
    @Autowired
    private IPlusShuntRepository            shuntRepository;
    @Autowired
    private SwitchUtil                      switchUtil;
    @Autowired
    private IPlusOrderDefrayApplication     orderDefrayApplication;
    @Autowired
    private IPlusOrderRefundInfoApplication orderRefundInfoApplication;
    @Autowired
    private RedissonClient                  redissonClient;
    @Autowired
    private PlusOrderSnapshtoQueryModel plusOrderSnapshtoQueryModel;
    @Autowired
    private IPlusBlackRepository plusBlackRepository;
    @Autowired
    private ProfitHandlerContext profitHandlerContext;
    @Autowired
    private IMemberPlusInfoDetailRepository memberPlusInfoDetailRepository;
    @Autowired
    private IMemberPlusExclusiveRepository memberPlusExclusiveRepository;
    @Autowired
    private PlusOrderSeparateModel plusOrderSeparateModel;
    @Autowired
    private IAcmExternalRepository acmExternalRepository;
    @Autowired
    private IPlusOrderRepository orderRepository;
    @Autowired
    private IPastMemberRefundRecordRepository pastMemberRefundRecordRepository;

    private final IPlusOrderApplicationConverter converter = IPlusOrderApplicationConverter.instance;

    /**
     * 下单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PlusOrderAo createPlusOrder(PlusOrderCreateEvent plusOrderCreateEvent) {
        try {
            if (!configProperties.plusChannels.contains(String.valueOf(plusOrderCreateEvent.getChannelId()))) {
                throw new PlusAbyssException("非可购买会员渠道");
            }
            PlusOrderPayTypeEnum payType = PlusOrderPayTypeEnum.getByValue(
                    plusOrderCreateEvent.getPayType());
            ParamCheckUtils.checkNull(payType, "付款方式不能为空/错误");
            PlusProgramEntity programEntity = programQueryModel.getById(
                    plusOrderCreateEvent.getProgramId());
            ParamCheckUtils.checkNull(programEntity, "方案不存在");
            if (plusOrderCreateEvent.getBizSource() == null) {
                plusOrderCreateEvent.setBizSource(BizSourceEnum.YKD.getCode()); //设置默认值为宜口袋
            }
            // 校验
            plusOrderValidator.createOrderValidator(plusOrderCreateEvent, programEntity);
            // 创单
            PlusOrderEntity plusOrder = plusOrderModel.createPlusOrder(plusOrderCreateEvent,
                    programEntity);

            PlusOrderAo plusOrderAo = converter.toPlusOrderAo(plusOrder);
            CreateOrderContext context = plusOrderCreateEvent.getCreateOrderContext();
            // 20231120 zjf 开通会员逻辑：后付款 或 开通方式=全款且支付方式=划扣
            boolean openCard =
                    StringUtils.isNotBlank(plusOrder.getOrderSn()) && (payType.getPayAfter() || (
                            plusOrderCreateEvent.getPayType() == CommonConstant.ONE
                                    && context != null && context.getPayType() != null
                                    && context.getPayType() == CommonConstant.ONE));
            if (openCard) {
                log.info("后付费订单，开始开卡: userId = {}, orderSn = {}, programId = {}",
                        plusOrderCreateEvent.getUserId(), plusOrder.getOrderSn(),
                        plusOrderCreateEvent.getProgramId());
                // 同个类型会员重复开卡处理
                boolean validator = plusOrderValidator.repeatOrderValidator(plusOrder);
                if (!validator) {
                    PlusRepeatPayEvent plusRepeatPayEvent = converter.toPlusRepeatPayEvent(
                            plusOrderCreateEvent, plusOrder);
                    plusOrderAdapter.saveRepeatOrder(plusRepeatPayEvent, plusOrder);
                    throw new PlusAbyssException("短时间内请勿重复开通会员");
                }
                PlusOrderRelationCreateEvent event = converter.toPlusOrderRelationCreateEvent(
                        plusOrderCreateEvent, plusOrder);
                if (plusOrderCreateEvent.getCreateOrderContext() != null
                        && plusOrderCreateEvent.getCreateOrderContext().getRelationBusinessType()
                        != null) {
                    event.setRelationBusinessType(PlusOrderRelationBusinessType.getByCode(
                            plusOrderCreateEvent.getCreateOrderContext()
                                    .getRelationBusinessType()));
                }
                // 订单关联关系
                buildOrderRelation(event);
                // 开卡
                PlusMemberCardOpenEvent cardOpenEvent = converter.toPlusOrderCreateEvent(
                        programEntity, plusOrder, plusOrderCreateEvent);
                memberCardApplication.openCard(cardOpenEvent);
                // 后付款会员开通成功处理
                PlusOpenResultEntity result = plusOrderModel.afterOrderForOpenCard(plusOrder,
                        plusOrderCreateEvent, programEntity);
                plusOrderAo.setLeadCode(result != null ? result.getLeadCode() : null);
                plusOrderAo.setOpenCard(true);
                // 合同签署
                plusOrderModel.signContract(plusOrder, plusOrderCreateEvent, CommonConstant.ONE);
            }
            return plusOrderAo;
        } catch (Exception e) {
            LogUtil.printLog(e, "创建会员订单异常");
            plusOrderModel.saveInvalidOrder(plusOrderCreateEvent.getUserId(),
                    plusOrderCreateEvent.getProgramId());
            if (e instanceof PlusAbyssException) {
                throw new PlusAbyssException(e.getMessage());
            }
            throw new PlusAbyssException("创建会员订单异常");
        }
    }

    @Override
    public PlusOrderAo createVirtualOrder(VirtualOrderCreateEvent createEvent) {
        VirtualCheckEvent virtualCheckEvent = converter.toVirtualCheckEvent(createEvent);
        Integer userId = virtualCheckEvent.getUserId();
        String productSku = virtualCheckEvent.getProductSku();
        // 是否能购买
        VirtualCheckResultEntity result = canBuyVirtualProduct(createEvent);
        if (result.getCanBuy() == CommonConstant.ZERO) {
            //不能买
            log.info("虚拟商品下单校验未通过:memberId={},sku={}", userId, productSku);
            // 20230103 zjf 修改返回内容
            throw new PlusAbyssException("您暂时不可购买此权益");
        }
        createEvent.setProfitTypeId(result.getProfitTypeId());
        createEvent.setProgramName(result.getProgramName());
        // 提交订单
        OrderEntity virtualOrder = plusOrderModel.createVirtualOrder(createEvent, result);
        return converter.toPlusOrderAo(virtualOrder);
    }

    /**
     * 虚拟商品权益下单
     */
    @Override
    public PlusOrderAo createVirtualGoodsOrder(VirtualGoodsOrderCreateEvent createEvent) {
        VirtualGoodsCheckEvent checkEvent = converter.toVirtualGoodsCheckEvent(createEvent);
        // 是否能领取
        VirtualGoodsCheckResultEntity checkResult = canBuyVirtualGoods(checkEvent);
        if (Objects.equals(checkResult.getBuyButtonState(),
                BuyButtonStateEnum.RECEIVED.getCode())) {
            Integer userId = checkEvent.getUserId();
            log.info("权益0元发放虚拟商品下单校验未通过:userId={},productId={}", userId,
                    checkResult.getProductId());
            throw new PlusAbyssException("您暂时不可领取此权益");
        }
        createEvent.setProgramId(checkResult.getProgramId());
        createEvent.setProgramName(checkResult.getProgramName());
        createEvent.setProductId(checkResult.getProductId());
        createEvent.setProductSku(checkResult.getProductSku());
        // 提交订单
        OrderEntity virtualOrder = plusOrderModel.createVirtualGoodsOrder(createEvent, checkResult);
        return converter.toPlusOrderAo(virtualOrder);
    }

    @Override
    public PlusOrderAo createPlusProductOrder(PlusProductOrderCreateEvent createEvent) {
        ProductCheckEvent event = converter.toPlusProductCheckEvent(createEvent);
        //检验是否满足购买条件
        ProductCheckResultEntity checkResult = canBuyProduct(event);
        if (!checkResult.isCanBuy()) {
            throw new PlusAbyssException(checkResult.getReason());
        }
        OrderEntity plusProductOrder = plusOrderModel.createPlusProductOrder(createEvent,
                checkResult);
        return converter.toPlusOrderAo(plusProductOrder);
    }

    @Override
    public VirtualCheckResultEntity canBuyVirtualProduct(VirtualOrderCreateEvent createEvent) {
        VirtualCheckEvent virtualCheckEvent = converter.toVirtualCheckEvent(createEvent);
        Integer userId = createEvent.getUserId();
        String sku = createEvent.getProductSku();
        log.info("校验虚拟商品是否可购买开始:userId={},sku={}", userId, sku);
        VirtualCheckResultEntity result = profitsQueryModel.canBuyVirtualProduct(virtualCheckEvent);
        if (result.getCanBuy() == CommonConstant.ZERO) {
            return result;
        }
        MemberPlusInfoDetailEntity infoDetail = result.getInfoDetail();
        // 限制逻辑处理
        if (!memberPlusVirtualAdapter.virtualLimitHandler(virtualCheckEvent, result, infoDetail,
                result.getProfitTypeId())) {
            log.info("购买虚拟权益校验已限制,无法购买，{},{}", userId, result.getTipType());
            result.setCanBuy(CommonConstant.ZERO);
            return result;
        }
        //设置折扣
        result.setPlusOrderSn(infoDetail.getOrderSn());
        result.setProgramName(infoDetail.getConfigName());
        result.setDiscountRate(result.getVirtualGoods().getDiscountRate());
        result.setCanBuy(CommonConstant.ONE);
        String resultStr = JSON.toJSONString(result);
        log.info("查询虚拟商品是否可购买返回,userId:{},sku:{},result:{}", userId, sku, resultStr);
        return result;
    }

    /**
     * 检查用户是否可以领取虚拟商品权益
     */
    @Override
    public VirtualGoodsCheckResultEntity canBuyVirtualGoods(VirtualGoodsCheckEvent checkEvent) {
        Integer userId = checkEvent.getUserId();
        log.info("校验虚拟商品权益是否可购买开始,userId:{}", userId);
        return profitsQueryModel.canBuyVirtualGoods(checkEvent);
    }

    @Override
    public ProductCheckResultEntity canBuyProduct(ProductCheckEvent event) {
        ProductCheckResultEntity entity = profitsQueryModel.canBuyProduct(event);
        if (!entity.isCanBuy()) {
            return entity;
        }
        // 分类限购校验
        boolean limitResult = memberPlusVirtualAdapter.productLimitHandler(event, entity,
                entity.getDetail());
        if (!limitResult) {
            log.info("会员商品分类购买限制校验未通过：{}", JSON.toJSONString(entity));
            entity.setBuyButtonState(BuyButtonStateEnum.RECEIVED.getCode());
            entity.setCanBuy(false);
            return entity;
        }
        entity.setBuyButtonState(BuyButtonStateEnum.CAN_BUY.getCode());
        entity.setCanBuy(true);
        return entity;
    }

    @Override
    public Boolean canBuyPlusProduct(ProductCheckEvent event) {
        return profitsQueryModel.canBuyPlusProduct(event);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PlusOrderCancelAo cancelPlusOrder(PlusOrderCancelEvent plusOrderCancelEvent) {
        PlusCancelTypeEnum cancelType = PlusCancelTypeEnum.getByValue(
                plusOrderCancelEvent.getCancelType());
        ParamCheckUtils.checkNull(cancelType, "取消类型不能为空/错误");
        plusOrderValidator.cancelOrderValidator(plusOrderCancelEvent);
        // 是否有效
        PlusOrderEntity plusOrderEntity = plusOrderQueryModel.getByOrderSn(
                plusOrderCancelEvent.getPlusOrderSn());
        if (plusOrderEntity == null) {
            throw new PlusAbyssException("无效的订单号");
        }
        plusOrderCancelEvent.setChannelId(plusOrderEntity.getChannelId());
        plusOrderCancelEvent.setUserId(plusOrderEntity.getUserId());
        plusOrderCancelEvent.setProgramId(plusOrderEntity.getProgramId());
        plusOrderCancelEvent.setConfigId(plusOrderEntity.getConfigId());
        // 取消前校验
        PlusOrderCancelAo entity = checkPreCancel(plusOrderCancelEvent);
        if (!entity.isCanBeCancel()) {
            // 不可取消
            throw new PlusAbyssException(String.valueOf(entity.getCode()), entity.getReason());
        }
        // 取消小额月卡续费
        if (plusOrderEntity.getConfigId() == JuziPlusEnum.XEYK_CARD.getCode()) {
            CancelRenewEvent event = converter.toCancelRenewEvent(plusOrderEntity.getOrderSn(),
                    cancelType.getName(), CommonConstant.TWO);
            cancelRenew(event);
        }
        // 取消会员身份移动周期的会员信息列表
        List<MemberPlusInfoDetailExtPo> moveList = null;
        // 取消会员身份（过期取消不需要取消会员身份）
        if (cancelType != PlusCancelTypeEnum.EXPIRE_PAYMENT) {
            PlusMemberCardCancelEvent cardCancelEvent = new PlusMemberCardCancelEvent();
            cardCancelEvent.setPlusOrderSn(plusOrderEntity.getOrderSn());
            moveList = memberCardApplication.cancelCard(cardCancelEvent);
        }
        PlusOrderCancelEntity cancelEntity = converter.toPlusOrderCancelEntity(entity);
        // 取消订单
        plusOrderModel.cancelOrder(plusOrderCancelEvent, plusOrderEntity, cancelEntity, moveList);
        // 取消权益
        memberPlusApplication.cancelByOrderSn(plusOrderCancelEvent, plusOrderEntity, moveList);
        // 取消订单关联关系和重置达成条件值
        cancelAfter(plusOrderEntity);
        // 保存回调三方任务
        if (plusOrderCancelEvent.isSaveNoticeTask()) {
            PlusOrderNoticeEvent event = converter.toPlusOrderNoticeEvent(plusOrderEntity,
                    OrderNoticeTypeEnum.REFUND_CARD.getCode());
            noticeApplication.saveNoticeTask(event);
        }
        // 删除会员缓存信息
        memberCardApplication.delMemberCacheInfo(plusOrderEntity.getChannelId(),
                plusOrderEntity.getUserId(), plusOrderEntity.getConfigId());
        //使用了加速权益，加入会员黑名单
        try {
            blackPlusUser(plusOrderEntity);
        } catch (Exception e) {
            log.error("加入会员黑名单失败,uid：{}，orderSn：{}", plusOrderEntity.getUserId(),plusOrderEntity.getOrderSn(), e);
        }
        return entity;
    }

    @Override
    public void cancelRepOrder(PlusOrderCancelEvent plusOrderCancelEvent,
            PlusOrderCancelEntity cancelEntity) {
        PlusOrderEntity plusOrderEntity = plusOrderQueryModel.getByOrderSn(
                plusOrderCancelEvent.getPlusOrderSn());
        if (plusOrderEntity == null) {
            throw new PlusAbyssException("无效的订单号");
        }
        plusOrderModel.cancelRepOrder(plusOrderCancelEvent, cancelEntity, plusOrderEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updOrderStateByCustomer(UpdOrderStateEvent event) {
        log.info("客服操作变更会员订单状态开始：{}", JSON.toJSONString(event));
        String orderSn = event.getOrderSn();
        // 校验
        PlusOrderEntity plusOrder = plusOrderValidator.updOrderStateValidator(event);
        List<MemberPlusInfoDetailExtPo> moveList = null;
        // 变更为取消状态
        if (event.getOrderState() == PlusOrderStateEnum.CANCELED.getCode()) {
            // 取消小额月卡续费
            if (plusOrder.getConfigId() == JuziPlusEnum.XEYK_CARD.getCode()) {
                CancelRenewEvent renewEvent = converter.toCancelRenewEvent(orderSn,
                        "客服修改状态-取消会员", CommonConstant.TWO);
                cancelRenew(renewEvent);
            }
            // 会员未过期：取消会员身份
            if (LocalDateTimeUtils.compareDate(new Date(), plusOrder.getEndTime()) == -1) {
                log.info("线下处理修改为取消-会员未过期，开始取消会员：{}", orderSn);
                // 取消会员身份
                PlusMemberCardCancelEvent cardCancelEvent = new PlusMemberCardCancelEvent();
                cardCancelEvent.setPlusOrderSn(orderSn);
                moveList = memberCardApplication.cancelCard(cardCancelEvent);
                // 取消权益
                PlusOrderCancelEvent cancelEvent = converter.toPlusOrderCancelEvent(event,
                        plusOrder, PlusCancelTypeEnum.CUSTOMER_CANCEL.getValue(), false);
                memberPlusApplication.cancelByOrderSn(cancelEvent, plusOrder, moveList);
            }
        }
        // 修改订单信息
        plusOrderModel.updOrderByCustomer(event, plusOrder, moveList);
        // 如果是取消订单，需要删除关联关系和重置达成值
        if (event.getOrderState() == PlusOrderStateEnum.CANCELED.getCode()) {
            // 取消订单关联关系和重置达成条件值（小额月卡订单支付成功笔数）
            cancelAfter(plusOrder);
            // 保存退卡通知任务
            PlusOrderNoticeEvent noticeEvent = converter.toPlusOrderNoticeEvent(plusOrder,
                    OrderNoticeTypeEnum.REFUND_CARD.getCode());
            noticeApplication.saveNoticeTask(noticeEvent);
        } else {
            // 生成小额月卡续费计划
            MemberPlusRenewPlanCreateEvent renewPlanCreateEvent = memberPlusRenewPlanFactory.toRenewPlanCreateEvent(
                    plusOrder);
            if (renewPlanCreateEvent != null) {
                renewPlanApplication.generate(renewPlanCreateEvent);
            }
            // 重置达成条件值（小额月卡订单支付成功笔数）
            PlusOrderPayFinishEvent payFinishEvent = getPlusOrderPayFinishEvent(plusOrder);
            memberPlusSendPlanApplication.addConditionReachEvent(payFinishEvent);
        }
    }

    /**
     * 取消订单后做的事
     * <p>取消订单关联关系和重置达成条件值</p>
     */
    private void cancelAfter(PlusOrderEntity plusOrder) {
        // 取消订单绑定关系
        clearOrderRelation(PlusOrderRelationBusinessType.FXK.getCode(), plusOrder.getOrderSn());
        // 重新计算结清返现权益达成笔数
        PlusOrderCancelFinishEvent payFinishEvent = new PlusOrderCancelFinishEvent();
        payFinishEvent.setUserId(plusOrder.getUserId());
        payFinishEvent.setChannelId(plusOrder.getChannelId());
        payFinishEvent.setPlusOrderSn(plusOrder.getOrderSn());
        memberPlusSendPlanApplication.addConditionReachEvent(payFinishEvent);
    }

    @Override
    public PlusOrderCancelAo checkPreCancel(PlusOrderCancelEvent plusOrderCancelEvent) {
        // 退款结果回调不需要再进行取消校验
        if (plusOrderCancelEvent.isNotify()) {
            PlusOrderCancelAo entity = new PlusOrderCancelAo();
            entity.setMoneyBack(plusOrderCancelEvent.getRefundOrderAmount());
            entity.setCanBeCancel(true);
            log.info("退款结果回调不需要再进行取消校验：{}", JSON.toJSONString(entity));
            return entity;
        }
        PlusCancelTypeEnum cancelType = PlusCancelTypeEnum.getByValue(
                plusOrderCancelEvent.getCancelType());
        Preconditions.checkNotNull(cancelType, "取消类型不能为空/错误");
        // 是否有效
        PlusOrderEntity plusOrderEntity = plusOrderQueryModel.getByOrderSn(
                plusOrderCancelEvent.getPlusOrderSn());
        Preconditions.checkNotNull(plusOrderEntity, "订单不存在");
        PlusOrderCancelAo entity = new PlusOrderCancelAo();
        // 返回对象
        plusOrderCancelEvent.setChannelId(plusOrderEntity.getChannelId());
        plusOrderCancelEvent.setUserId(plusOrderEntity.getUserId());
        plusOrderCancelEvent.setProgramId(plusOrderEntity.getProgramId());
        plusOrderCancelEvent.setConfigId(plusOrderEntity.getConfigId());
        //后台操作取消需要校验逾期
        if (Objects.equals(plusOrderCancelEvent.getOptUserType(), OptUserTypeEnum.ADMIN.getUserType())
            && !Objects.isNull(plusOrderCancelEvent.getOptUserId())) {
            //逾期校验
            try {
                log.info("orderSn:{}, 退款进行逾期校验", plusOrderCancelEvent.getPlusOrderSn());
                PlanSummaryResp planSummaryResp = acmExternalRepository.
                        queryRepayPlanSummary(plusOrderEntity.getChannelId(), plusOrderCancelEvent.getUserId());
                if (planSummaryResp.getOverdueBillAmount() != null
                        && planSummaryResp.getOverdueBillAmount().compareTo(BigDecimal.ZERO) > 0) {
                    //判断当前操作用户是否为可操作用户
                    if (Arrays.stream(configProperties.overdueCancelUsers.split(",")).
                            noneMatch(t -> t.equals(plusOrderCancelEvent.getOptUserId().toString()))) {
                        entity.setCode(500);
                        entity.setReason("用户有逾期的账单，无法进行会员退费");
                        return entity;
                    }
                }
            } catch (Exception e) {
                log.error("用户逾期校验异常", e);
                entity.setCode(500);
                entity.setReason("用户逾期校验异常，请重试");
                return entity;
            }
        }
        // 20240309 zjf 获取人脸识别结果（只有取消前检查时候需要获取此信息返回给客服展示，不管能否取消成功）
        setUserFaceAuth(plusOrderCancelEvent, entity);
        try {
            // 取消规则校验
            this.cancelPreCheckRule(cancelType, plusOrderCancelEvent);
            entity.setCanBeCancel(true);
        } catch (Exception e) {
            entity.setCode(e instanceof PlusAbyssException ? Integer.parseInt(
                    ((PlusAbyssException) e).getErrorCode()) : 500);
            entity.setReason(
                    e instanceof PlusAbyssException ? e.getMessage() : "检查会员能否取消异常");
            LogUtil.printLog(e, "检查会员能否取消异常");
        }
        // 取消规则校验：不可取消
        if (!entity.isCanBeCancel()) {
            return entity;
        }
        // 计算订单退款金额（包含差价扣减计算）
        PlusOrderCancelEntity cancelEntity = plusOrderModel.calOrderRefundAmount(plusOrderEntity,
                plusOrderCancelEvent);
        // 扣减差价超过会员价：不可取消(hxf 2024.1.23 直接返回到外层做判断)
        MemberPlusLmkVirtualEntity lmk = profitsQueryModel.getMemberLmkVirtualByOrderSn(
                plusOrderEntity.getOrderSn());
        cancelEntity.setVirtualProductName(lmk != null ? lmk.getProductName() : null);
        log.info("取消前校验-计算退款金额返回：{}", JSON.toJSONString(cancelEntity));
        PlusOrderCancelAo cancelAo = converter.toPlusOrderCancelAo(cancelEntity);
        // 20240309 zjf 获取人脸识别结果（只有取消前检查时候需要获取此信息返回给客服展示，不管能否取消成功）
        setUserFaceAuth(plusOrderCancelEvent, cancelAo);
        return cancelAo;
    }

    /**
     * 设置人脸识别信息
     */
    private void setUserFaceAuth(PlusOrderCancelEvent event, PlusOrderCancelAo entity) {
        if (event.isNeedUserFaceAuth()) {
            entity.setUserFaceAuth(
                    workOrderExternalRepository.getUserLastAuthRecord(event.getUserId()));
        }
    }

    /**
     * 取消规则校验
     *
     * @Param cancelType 取消类型
     * <AUTHOR>
     * @version 1.0
     * @date 2024/1/9 14:53
     */
    @Override
    public void cancelPreCheckRule(PlusCancelTypeEnum cancelType,
            PlusOrderCancelEvent plusOrderCancelEvent) {
        // 只有非代付取消需要走取消规则校验
        // 未过期：走无条件取消规则校验  已过期：不走取消规则校验，这2种取消方式调此方法只计算退款金额，扣减生活权益差价等信息
        if (PlusCancelTypeEnum.isNotDefrayCancel(cancelType.getValue())) {
            log.info("取消订单校验规则开始：{}", plusOrderCancelEvent.getPlusOrderSn());
            // 取消场景code转换
            Integer cancelSense = cancelType.getCancelSense();
            if (cancelSense == null) {
                throw new PlusAbyssException("未配置取消场景code");
            }
            // 校验取消规则
            alphaExternalRepository.cancelPreCheck(plusOrderCancelEvent, cancelSense);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDuctPlan(CreateDeductPlanEvent createDeductPlanEvent) {
        log.info("创建划扣计划开始: param = {}", JSON.toJSONString(createDeductPlanEvent));
        //创建划扣计划
        plusOrderModel.createDuctPlan(createDeductPlanEvent);
        log.info("创建划扣计划结束: param = {}", JSON.toJSONString(createDeductPlanEvent));
    }

    @Override
    public PlusOrderDeductAo deduct(PlusDeductEvent deductEvent) {
        //前置校验
        try {
            plusOrderModel.preDeduct(deductEvent);
        } catch (PlusAbyssException e) {
            log.info("划扣前置校验未通过,业务终止：", e);
            PlusDeductLogEntity logEntity = converter.checkToDeductLogEntity(deductEvent,
                    PlusDeductLogTypeEnum.getByCode(Integer.parseInt(e.getErrorCode())),
                    e.getMessage());
            if (StringUtils.isEmpty(deductEvent.getPlusOrderSn())
                    && deductEvent.getPlusOrderEntity() != null) {
                logEntity.setPlusOrderSn(deductEvent.getPlusOrderEntity().getOrderSn());
            }
            plusOrderDeductPlanModel.saveDeductLog(logEntity);
            PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
            resEntity.setOptStatus(DeductResultStateEnum.FAIL.getCode());
            resEntity.setRemark(e.getMessage());
            return converter.toPlusDeductAo(resEntity);
        } catch (Exception e) {
            log.info("划扣前置校验未通过,未知异常：", e);
            PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
            resEntity.setOptStatus(DeductResultStateEnum.ERROR.getCode());
            resEntity.setRemark("划扣前置校验异常");
            PlusDeductLogEntity logEntity = converter.checkToDeductLogEntity(deductEvent,
                    PlusDeductLogTypeEnum.LOG_TYPE_0, "划扣前校验异常");
            plusOrderDeductPlanModel.saveDeductLog(logEntity);
            return converter.toPlusDeductAo(resEntity);
        }
        //划扣时会员订单分账校验
        if (!deductBeforeValidOrderSeparate(deductEvent)) {
            log.info("划扣前置会员订单分账校验未通过, plusOrderSn:{}", deductEvent.getPlusOrderEntity().getOrderSn());
            PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
            resEntity.setOptStatus(DeductResultStateEnum.ERROR.getCode());
            resEntity.setRemark("划扣前置会员订单分账校验异常");
            return converter.toPlusDeductAo(resEntity);
        }
        //划扣时用户主动支付校验
        if (!deductBefore(deductEvent)) {
            log.info("划扣前置校验未通过, plusOrderSn:{}", deductEvent.getPlusOrderEntity().getOrderSn());
            PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
            resEntity.setOptStatus(DeductResultStateEnum.ERROR.getCode());
            resEntity.setRemark("划扣前置校验异常");
            return converter.toPlusDeductAo(resEntity);
        }
        // hxf 2024.9.7 上线切换控制
        if (switchUtil.isNew(deductEvent.getUserId())) {
            //设置系统代扣缓存标识
            log.info("会员订单代扣前设置缓存标识,plusOrderSn:{}", deductEvent.getPlusOrderEntity().getOrderSn());
            redisUtils.setEx(PLUS_ORDER_AFTER_HK + deductEvent.getPlusOrderEntity().getOrderSn(), "1",
                    configProperties.afterPayLockTime, TimeUnit.SECONDS);
            //新划扣申请
            PlusOrderDeductResEntity deductRes = plusOrderModel.deduct(deductEvent);
            //划扣申请后置处理
            deductAfter(deductEvent, deductRes);
            return converter.toPlusDeductAo(deductRes);
        } else {
            //旧划扣
            PlusOrderDeductResEntity deductRes = plusOrderModel.deductOld(deductEvent);
            //划扣后置处理
            deductAfterOld(deductEvent, deductRes);
            return converter.toPlusDeductAo(deductRes);
        }
    }

    @Override
    public void delayBatchDeduct() {
        log.info("批量延时划扣开始");
        List<PlusOrderDeductPlanEntity> delayDeductList = deductPlanModel.getDelayDeductList();
        if (CollectionUtils.isEmpty(delayDeductList)) {
            log.info("批量延迟划扣列表为空");
            return;
        }
        for (PlusOrderDeductPlanEntity entity : delayDeductList) {
            try {
                PlusDeductEvent event = converter.toPlusDeductEvent(entity,
                        PlusPayTypeEnum.PAY_TYPE_7);
                this.deduct(event);
            } catch (Exception e) {
                LogUtil.printLog(e, "延迟划扣执行异常");
                imRepository.sendImMessage(
                        "延迟划扣任务执行异常,会员单号：" + entity.getPlusOrderSn() + ",借款单号："
                                + entity.getOrderSn());
            }
        }
        log.info("批量延时划扣结束");
    }

    /**
     * 划扣后置处理（旧）
     */
    @Deprecated
    private void deductAfterOld(PlusDeductEvent deductEvent, PlusOrderDeductResEntity deductRes) {
        if (deductRes == null) {
            log.info("划扣后置处理未获取到划扣结果：{}", JSON.toJSONString(deductEvent));
            return;
        }
        PlusOrderEntity plusOrderEntity = deductEvent.getPlusOrderEntity();
        String plusOrderSn = plusOrderEntity.getOrderSn();
        log.info("划扣后置处理开始,plusOrderSn{},划扣结果：{}", plusOrderSn,
                JSON.toJSONString(deductRes));
        //处理状态 0 不处理、1 成功、2 失败、3 异常
        switch (deductRes.getOptStatus()) {
            case 0: {
                break;
            }
            case 1: {
                //成功
                log.info("划扣成功处理开始,plusOrderSn：{}", plusOrderSn);
                PlusDeductSuccessEvent successEvent = converter.toPlusDeductSuccessEvent(
                        deductEvent);
                successEvent.setPlusOrderSn(plusOrderSn);
                successEvent.setConfigId(plusOrderEntity.getConfigId());
                successEvent.setPlusOrderEntity(plusOrderEntity);
                successEvent.setResEntity(deductRes);
                handlerApplicationContext.deductAfterSucOld(successEvent);
                log.info("划扣成功处理结束,plusOrderSn：{}", plusOrderSn);
                break;
            }
            case 2: {
                //失败
                log.info("划扣失败处理开始,plusOrderSn：{}", plusOrderSn);
                PlusDeductFailEvent failEvent = converter.toPlusDeductFailEvent(deductEvent);
                failEvent.setResEntity(deductRes);
                failEvent.setConfigId(plusOrderEntity.getConfigId());
                failEvent.setPlusOrderEntity(plusOrderEntity);
                failEvent.setPlusType(deductEvent.getPlusType());
                handlerApplicationContext.deductAfterFailOld(failEvent);
                log.info("划扣失败处理结束,plusOrderSn：{}", plusOrderSn);
                break;
            }
            case 3: {
                //异常
                log.info("划扣异常处理开始,plusOrderSn：{}", plusOrderSn);
                PlusDeductExcEvent excEvent = converter.toPlusDeductExcEvent(deductEvent);
                excEvent.setConfigId(plusOrderEntity.getConfigId());
                excEvent.setPlusOrderSn(plusOrderSn);
                excEvent.setPlusOrderEntity(plusOrderEntity);
                excEvent.setChannelId(plusOrderEntity.getChannelId());
                handlerApplicationContext.deductAfterExcOld(excEvent);
                // 保存划扣异常日志
                PlusDeductLogEntity logEntity = converter.excToDeductLogEntity(excEvent,
                        PlusPayTypeEnum.PAY_TYPE_1.getCode(), PlusDeductLogTypeEnum.LOG_TYPE_8,
                        "划扣异常");
                plusOrderDeductPlanModel.saveDeductLog(logEntity);
                log.info("划扣异常处理结束,plusOrderSn：{}", plusOrderSn);
                break;
            }
            default: {
                log.info("划扣状态未知处理结束,plusOrderSn：{}", plusOrderSn);
                break;
            }
        }
    }

    /**
     * 划扣前置处理 1.校验用户是否正在主动支付
     */
    private boolean deductBefore(PlusDeductEvent deductEvent) {
        PlusOrderEntity plusOrderEntity = deductEvent.getPlusOrderEntity();
        if (redisUtils.hasKey(PLUS_ORDER_AFTER_ZD + plusOrderEntity.getOrderSn())) {
            log.info("orderSn:{}, 系统划扣时校验用户主动支付中", plusOrderEntity.getOrderSn());
            //加入延迟划扣
            PlusDeductFailEvent failEvent = converter.toPlusDeductFailEvent(deductEvent);
            failEvent.setConfigId(plusOrderEntity.getConfigId());
            failEvent.setPlusOrderEntity(plusOrderEntity);
            failEvent.setPlusType(deductEvent.getPlusType());
            handlerApplicationContext.deductBeforeFail(failEvent);
            return false;
        }
        return true;
    }

    /**
     * 划扣前置处理 1.校验会员订单分账记录
     */
    private boolean deductBeforeValidOrderSeparate(PlusDeductEvent deductEvent) {
        String orderSn = deductEvent.getPlusOrderEntity().getOrderSn();
        log.info("划扣前置处理校验会员订单分账,订单号:{}", orderSn);
        List<PlusOrderSeparateEntity> orderSeparateEntities = plusOrderSeparateModel.getPlusOrderSeparate(orderSn);
        log.info("划扣前置处理校验会员订单分账,记录:{}", JSON.toJSONString(orderSeparateEntities));
        if (Objects.equals(deductEvent.getPlusOrderEntity().getPayType(), PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
            //分期支付，一共分2期。支付成功1笔，可继续划扣
            return orderSeparateEntities.stream().filter(entity -> Objects.equals(SeparateStateEnum.SUCCESS.getCode(), entity.getSeparateState())).count() == 1;
        }
        for (PlusOrderSeparateEntity entity : orderSeparateEntities) {
            if (Objects.equals(SeparateStateEnum.SUCCESS.getCode(), entity.getSeparateState())) {
                return false;
            }
            if (Objects.equals(OrderPayActionEnum.PAY_ACTION_2.getCode(), entity.getOrderPayAction())
                    && Objects.equals(SeparateStateEnum.PROCESSING.getCode(), entity.getSeparateState())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 划扣后置处理(新)
     */
    private void deductAfter(PlusDeductEvent deductEvent, PlusOrderDeductResEntity deductRes) {
        if (deductRes == null) {
            log.info("划扣申请后置处理未获取到划扣申请结果：{}", JSON.toJSONString(deductEvent));
            return;
        }
        PlusOrderEntity plusOrderEntity = deductEvent.getPlusOrderEntity();
        String plusOrderSn = plusOrderEntity.getOrderSn();
        log.info("划扣申请后置处理开始,plusOrderSn{},划扣结果：{}", plusOrderSn,
                JSON.toJSONString(deductRes));
        //处理状态 0 不处理、1 成功、2 失败、3 异常
        switch (deductRes.getOptStatus()) {
            case 0: {
                break;
            }
            case 1: {
                //成功
                log.info("划扣申请成功处理开始,plusOrderSn：{}", plusOrderSn);
                PlusDeductSuccessEvent successEvent = converter.toPlusDeductSuccessEvent(
                        deductEvent);
                successEvent.setPlusOrderSn(plusOrderSn);
                successEvent.setConfigId(plusOrderEntity.getConfigId());
                successEvent.setPlusOrderEntity(plusOrderEntity);
                successEvent.setResEntity(deductRes);
                handlerApplicationContext.deductAfterSuc(successEvent);
                log.info("划扣申请成功处理结束,plusOrderSn：{}", plusOrderSn);
                break;
            }
            case 2: {
                //失败
                log.info("划扣申请失败处理开始,plusOrderSn：{}", plusOrderSn);
                PlusDeductFailEvent failEvent = converter.toPlusDeductFailEvent(deductEvent);
                failEvent.setResEntity(deductRes);
                failEvent.setConfigId(plusOrderEntity.getConfigId());
                failEvent.setPlusOrderEntity(plusOrderEntity);
                failEvent.setPlusType(deductEvent.getPlusType());
                handlerApplicationContext.deductAfterFail(failEvent);
                log.info("划扣申请失败处理结束,plusOrderSn：{}", plusOrderSn);
                break;
            }
            case 3: {
                //异常
                log.info("划扣申请异常处理开始,plusOrderSn：{}", plusOrderSn);
                PlusDeductExcEvent excEvent = converter.toPlusDeductExcEvent(deductEvent);
                excEvent.setConfigId(plusOrderEntity.getConfigId());
                excEvent.setPlusOrderSn(plusOrderSn);
                excEvent.setPlusOrderEntity(plusOrderEntity);
                excEvent.setChannelId(plusOrderEntity.getChannelId());
                handlerApplicationContext.deductAfterExc(excEvent);
                // 保存划扣异常日志
                PlusDeductLogEntity logEntity = converter.excToDeductLogEntity(excEvent,
                        PlusPayTypeEnum.PAY_TYPE_1.getCode(), PlusDeductLogTypeEnum.LOG_TYPE_8,
                        "划扣申请异常");
                plusOrderDeductPlanModel.saveDeductLog(logEntity);
                log.info("划扣申请异常处理结束,plusOrderSn：{}", plusOrderSn);
                break;
            }
            default: {
                log.info("划扣申请状态未知处理结束,plusOrderSn：{}", plusOrderSn);
                break;
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PlusPayCallbackAo payCallBack(PlusOrderPayCallbackEvent callbackEvent) {
        log.info("会员订单支付回调处理开始：{}", JSON.toJSON(callbackEvent));
        PlusPayCallbackAo ao = new PlusPayCallbackAo();
        // 是否有效
        PlusOrderEntity plusOrderEntity = plusOrderQueryModel.getByOrderSn(
                callbackEvent.getOrderSn());
        ParamCheckUtils.checkNull(plusOrderEntity, "订单不存在");
        ao.setProgramId(plusOrderEntity.getProgramId());
        ao.setChannelId(plusOrderEntity.getChannelId());
        ao.setConfigId(plusOrderEntity.getConfigId());

        log.info("plusOrderEntity.getPayType():{}", plusOrderEntity.getPayType());
        //分期支付，首次开通会员，修改payAmount值。第二次支付，修改payAmount值，订单状态改为已支付。
        if (PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue().equals(plusOrderEntity.getPayType()) && Objects.equals(OrderStateEnum.已完成.getState(), callbackEvent.getOrderState())) {
            callbackEvent.setFlag(CommonConstant.TWO);
        }

        // 1=开通会员mq 2=支付成功mq
        switch (callbackEvent.getFlag()) {
            case CommonConstant.ONE:
                // 开卡
                PlusProgramEntity programEntity = programQueryModel.getById(
                        plusOrderEntity.getProgramId());
                ParamCheckUtils.checkNull(programEntity, "方案不存在");
                PlusOrderPayTypeEnum plusPayTypeEnum = PlusOrderPayTypeEnum.getByValue(
                        plusOrderEntity.getPayType());
                ParamCheckUtils.checkNull(plusPayTypeEnum, "付款类型不存在");
                // 非后付款订单，开卡
                if (!plusPayTypeEnum.getPayAfter()) {
                    boolean validator = plusOrderValidator.repeatOrderValidator(plusOrderEntity);
                    if (!validator) {
                        // 同个类型会员重复开卡处理
                        PlusRepeatPayEvent plusRepeatPayEvent = converter.toPlusRepeatPayEvent(
                                callbackEvent, plusOrderEntity);
                        plusOrderAdapter.saveRepeatOrder(plusRepeatPayEvent, plusOrderEntity);
                        return ao;
                    }
                    // 开卡
                    PlusMemberCardOpenEvent plusOrderCreateEvent = converter.toPlusOrderCreateEvent(
                            programEntity, plusOrderEntity, callbackEvent);
                    memberCardApplication.openCard(plusOrderCreateEvent);
                    ao.setOpenCard(true);
                    // 合同签署
                    plusOrderModel.signContract(plusOrderEntity, null, CommonConstant.ONE);
                }

                // 刷新权益计划列表，补增计划任务-开卡礼、月享红包、多买多送、拒就赔
                PlusProfitSendPlanEvent event = converter.toPlusProfitSendPlanEvent(
                        callbackEvent.getOrderSn(), plusOrderEntity.getProgramId(),
                        plusOrderEntity.getConfigId(), PlusConstant.MODEL_SEND_CONTROL_LIST);
                profitsSendPlanApplication.refreshProfitSendPlan(event);
                // 处理支付回调
                plusOrderModel.payCallBackForOpenCard(callbackEvent, plusOrderEntity);
                // 非后付款订单,绑定与借款订单的关系
                bindOrderRelation(callbackEvent, plusOrderEntity);
                // 3. 小额月卡生成续费计划
                MemberPlusRenewPlanCreateEvent renewPlanCreateEvent = memberPlusRenewPlanFactory.toRenewPlanCreateEvent(
                        plusOrderEntity);
                if (renewPlanCreateEvent != null) {
                    renewPlanApplication.generate(renewPlanCreateEvent);
                }
                if (plusOrderEntity.getConfigId() == JuziPlusEnum.XEYK_CARD.getCode()) {
                    // 注册发放计划任务达成事件
                    PlusOrderPayFinishEvent payFinishEvent = getPlusOrderPayFinishEvent(
                            plusOrderEntity);
                    memberPlusSendPlanApplication.addConditionReachEvent(payFinishEvent);
                }
                break;
            case CommonConstant.TWO:
                log.info("分期支付，第二次支付成功。 修改payamount、订单状态为已支付:{}", JSON.toJSONString(callbackEvent));
                //分期支付，支付成功。 修改payamount、订单状态为已支付
                PlusOrderInfoPo update = new PlusOrderInfoPo();
                update.setOrderSn(plusOrderEntity.getOrderSn());
                update.setPayAmount(plusOrderEntity.getOrderAmount());
                update.setOrderState(PlusOrderStateEnum.PAY_SUCCESS.getCode());
                Date payTime = new Date();
                update.setPayTime(payTime);
                update.setCallTime(payTime);
                orderRepository.updatePlusOrderInfoByOrderSn(update);
                break;
            default:
                log.info("支付回调,未知类型,不处理");
                break;
        }
        log.info("会员订单支付回调处理结束：{}", callbackEvent.getOrderSn());
        return ao;
    }

    @Override
    public void unPayCancelCallBack(String plusOrderSn) {
        plusOrderModel.unPayCancelCallBack(plusOrderSn);
    }

    private PlusOrderPayFinishEvent getPlusOrderPayFinishEvent(PlusOrderEntity plusOrderEntity) {
        PlusOrderPayFinishEvent payFinishEvent = new PlusOrderPayFinishEvent();
        payFinishEvent.setUserId(plusOrderEntity.getUserId());
        payFinishEvent.setChannelId(plusOrderEntity.getChannelId());
        payFinishEvent.setPlusOrderSn(plusOrderEntity.getOrderSn());
        payFinishEvent.setPayAmount(plusOrderEntity.getPayAmount());
        return payFinishEvent;
    }

    /**
     * 非后付款订单,绑定与借款订单的关系
     *
     * <AUTHOR>
     * @date 2023/9/21 09:51
     **/
    private void bindOrderRelation(PlusOrderPayCallbackEvent callbackEvent,
            PlusOrderEntity plusOrderEntity) {
        PlusOrderPayTypeEnum plusPayTypeEnum = PlusOrderPayTypeEnum.getByValue(
                plusOrderEntity.getPayType());
        Preconditions.checkNotNull(plusPayTypeEnum, "付款类型不存在");
        if (!plusPayTypeEnum.getPayAfter()
                && plusOrderEntity.getConfigId() == JuziPlusEnum.XEYK_CARD.getCode()) {
            log.info("支付结果回调,开始处理小额月卡全款单的订单关联关系：{}",
                    callbackEvent.getOrderSn());
            PlusOrderRelationCreateEvent event = converter.toPlusOrderRelationCreateEvent(
                    plusOrderEntity);
            event.setRelationBusinessType(PlusOrderRelationBusinessType.FXK);
            String key = PlusConcatUtils.symbolBelowStr(RedisConstantPrefix.PLUS_RELATION,
                    plusOrderEntity.getOrderSn(), PlusOrderRelationBusinessType.FXK.getCode());
            String loanSn = redisUtils.get(key);
            log.info("获取缓存中的借款单号缓存：{}", loanSn);
            event.setOrderSn(loanSn);
            // 绑定借款单与会员单关系
            buildOrderRelation(event);
        }
    }

    /**
     * 会员单与业务订单建立绑定关系
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildOrderRelation(PlusOrderRelationCreateEvent plusOrderRelationCreateEvent) {
        plusOrderModel.buildOrderRelation(plusOrderRelationCreateEvent);
    }

    /**
     * 解除会员单与借款单的绑定关系
     */
    @Override
    public void clearOrderRelation(Integer businessType, String plusOrderSn) {
        plusOrderModel.clearOrderRelation(businessType, plusOrderSn);
    }

    /**
     * 取消订单的续费(小额月卡)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelRenew(CancelRenewEvent event) {
        if (event == null || StringUtils.isBlank(event.getOrderSn())) {
            throw new PlusAbyssException("会员单号不能为空");
        }
        String groupId = event.getOrderSn();
        MemberPlusRenewPlanEntity renewPlan = renewPlanQueryModel.getByPlusOrderSn(
                event.getOrderSn());
        if (renewPlan != null) {
            groupId = renewPlan.getGroupId();
        }
        PlusOrderEntity plusOrder = plusOrderQueryModel.getByOrderSn(groupId);
        if (plusOrder == null) {
            throw new PlusAbyssException("无效的订单号");
        }
        // 1. 订单取消续费标识
        plusOrderModel.cancelRenew(plusOrder, event.getRemark());
        // 2. 取消续费计划
        renewPlanApplication.cancel(groupId);
    }

    /**
     * 根据续费计划创建订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createPlusOrderByRenewPlan(MemberPlusRenewPlanEntity renewPlan) {
        // 1. 创建后付款订单
        // 1.1 查询订单归因标识
        // 通过group_id(第一笔会员订单号)查询订单归因标记，续费订单，只要第一笔订单有标记，则每笔续费都会有，反之则都没有
        OrderExtraVO orderExtraVO = iOrderExternalRepository.getBaseAndExtraByOrderSn(
                renewPlan.getGroupId());
        if (orderExtraVO == null) {
            throw new PlusAbyssException("续费订单失败-订单信息获取失败");
        }
        // huxf 2023.12.08 新增订单归因字段
        String ascribeTo =
                !Objects.isNull(orderExtraVO.getExtra()) ? orderExtraVO.getExtra().getAscribeTo()
                        : null;
        PlusOrderCreateEvent orderCreateEvent = converter.toPlusOrderCreateEvent(renewPlan,
                PlusOrderPayTypeEnum.PAY_AFTER.getValue(), OrderRenewEnum.RENEW.getCode(),
                ascribeTo);
        String plusOrderSn = null;
        RenewStateEnum renewStateEnum = RenewStateEnum.RENEWING;
        try {
            CreateOrderContext createOrderContext = new CreateOrderContext();
            createOrderContext.setContractNo(configProperties.juziContractNo);
            createOrderContext.setFirstOrderSn(renewPlan.getGroupId());
            orderCreateEvent.setCreateOrderContext(createOrderContext);
            PlusOrderAo plusOrderAo = createPlusOrder(orderCreateEvent);
            plusOrderSn = plusOrderAo.getOrderSn();
            renewStateEnum = RenewStateEnum.RENEWED;
            //计算划扣日期
            Date deductTime = renewPlanApplication.calPlanDeductTime(renewPlan.getPeriodStartTime(),
                    renewPlan.getPeriodEndTime());
            //创建划扣计划
            CreateDeductPlanEvent createDeductPlanEvent = converter.toCreateDeductPlanEvent(
                    deductTime, renewPlan, plusOrderSn);
            createDuctPlan(createDeductPlanEvent);
        } catch (Exception e) {
            log.error("续费计划创单异常, renewPlanId = {}, event = {}", renewPlan.getRenewPlanId(),
                    JSON.toJSONString(orderCreateEvent), e);
            renewStateEnum = RenewStateEnum.FAILED;
        }
        // 2. 更新续费计划状态及订单号
        renewPlanApplication.updateRenewState(renewPlan.getRenewPlanId(), renewStateEnum.getCode(),
                plusOrderSn);
        if (renewStateEnum == RenewStateEnum.FAILED) {
            log.info("续费计划创单异常, 等待重试， renewPlanId = {}", renewPlan.getRenewPlanId());
            if (renewPlan.getRenewRetryCount() >= 1) {
                imRepository.sendImMessage(
                        "续费计划创单异常, 等待重试， renewPlanId = " + renewPlan.getRenewPlanId());
            }
            return false;
        }
        /**
         * 结清返现需要查询同组会员单支付数量
         * 否则可能会造成：开通会员保存发放计划达成条件时,结清返现的【会员订单支付成功数量】条件达成值为0
         * 如果该笔会员订单一直不支付会导致达成值始终为0，但是实际上同组内已经支付达到2笔数量了，借款单结清时会返现不了
         * 所以小额月卡需要在生成发放计划后触发下【会员订单支付成功数量】达成事件，事件内部会查询订单支付成功数量并且更新相关状态
         */
        // 计算结清返现权益达成笔数
        log.info("小额月卡续费单开通成功,触发计算支付笔数达成条件：{}", plusOrderSn);
        PlusOrderPayFinishEvent payFinishEvent = new PlusOrderPayFinishEvent();
        payFinishEvent.setUserId(orderCreateEvent.getUserId());
        payFinishEvent.setChannelId(orderCreateEvent.getChannelId());
        payFinishEvent.setPlusOrderSn(plusOrderSn);
        memberPlusSendPlanApplication.addConditionReachEvent(payFinishEvent);
        return true;
    }

    /**
     * 根据续费计划创建订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPlusOrderByRenewPlans(List<MemberPlusRenewPlanEntity> renewPlans) {
        if (CollectionUtils.isEmpty(renewPlans)) {
            log.info("暂无需续费创单的续费计划");
            return;
        }
        renewPlans.forEach(renewPlan -> {
            try {
                createPlusOrderByRenewPlan(renewPlan);
            } catch (Exception e) {
                log.error("续费创单处理异常，请注意查收并处理，data = {}",
                        JSON.toJSONString(renewPlan), e);
            }
        });
    }

    @Override
    public void invalidOrder() {
        plusOrderModel.invalidOrder();
    }

    /**
     * 浩瀚后台-会员订单列表
     */
    @Override
    public PageResultEntity<PlusOrderInfoAo> getPlusOrderList(PlusOrderInfoQueryReq req) {
        PageResultEntity<PlusOrderInfoAo> result = new PageResultEntity<>();
        // 校验参数
        plusOrderValidator.checkParam(req);
        // 查询会员订单列表数据和分页总条数
        List<PlusOrderEntity> resultList = plusOrderQueryModel.pageList(req);
        result.setList(converter.toPlusOrderInfoAoList(resultList));
        result.setCount(plusOrderQueryModel.pageListCount(req));
        return result;
    }

    /**
     * 浩瀚后台-订单列表-查看详情
     */
    @Override
    public PlusOrderDetailAo getPlusOrderDetails(PlusOrderDetailQueryReq req) {
        plusOrderValidator.checkQueryDetailParam(req);
        log.info("会员订单详情,开始:{}", JSON.toJSONString(req));
        String orderSn = req.getOrderSn();
        //订单信息
        PlusOrderEntity orderEntity = plusOrderQueryModel.getByOrderSn(orderSn);
        if (Objects.isNull(orderEntity)) {
            log.info("会员订单详情,未查询到订单信息,订单号：{}", orderSn);
            return null;
        }
        PlusOrderDetailAo detailAo = new PlusOrderDetailAo();
        // 设置订单信息
        orderAdapter.setOrderInfoForPlusDetail(orderEntity, detailAo);
        // 设置订单分账信息/支付记录
        orderAdapter.setOrderSeparateRecord(orderEntity, detailAo);
        //设置退款信息
        orderAdapter.setRefundInfoForPlusDetail(orderEntity, detailAo);
        //设置用户信息
        orderAdapter.setMemberInfoForPlusDetail(orderEntity, detailAo);
        //设置权益信息
        orderAdapter.setProfitInfoForPlusDetail(orderEntity, detailAo);
        //设置融担咨询卡关联信息
        orderAdapter.setRelationInfoForPlusDetail(orderEntity, detailAo);
        log.info("会员订单详情,返回：{}", JSON.toJSONString(detailAo));

        return detailAo;
    }

    @Override
    public List<PlusProductOrderEntity> getProductOrderList(String plusOrderSn, Integer modelId) {
        return plusOrderQueryModel.getProductOrderList(plusOrderSn, modelId);
    }

    @Override
    public void expireCancelWaitAfterPayOrder(List<String> orderSn) {
        plusOrderModel.expireCancelWaitAfterPayOrder(orderSn);
    }

    @Override
    public void repayRenew(Integer userId, Integer channelId) {
        try {
            log.info("还款卡续费开始：{}", userId);
            // 检查是否能续费,获取续费信息和续费方案
            PlusRepayRenewEntity renewInfo = repayModel.getRenewInfo(userId, channelId);
            if (!renewInfo.isCanRenew()) {
                log.info("还款卡无法续费：{}", userId);
                return;
            }
            Integer configId = renewInfo.getRenewInfo().getConfigId();
            // 防止重复提交续费订单
            String redisKey = PlusConcatUtils.symbolBelowStr(
                    RedisConstantPrefix.MEMBER_PLUS_RENEW_ORDER, userId, configId);
            boolean lock = redisLock.lock(redisKey, "1", 300);
            if (!lock) {
                log.info("还款卡续费5分钟内重复提交：{}", renewInfo.getRenewInfo().getOrderSn());
                return;
            }
            // 创建会员订单
            PlusOrderCreateEvent event = converter.toPlusOrderCreateEvent(
                    OrderRenewEnum.RENEW.getCode(), renewInfo.getProgramId(), channelId,
                    renewInfo.getRenewInfo().getMoney(), userId,
                    PlusOrderPayTypeEnum.PAY_DEDUCT.getValue());
            PlusOrderAo plusOrder = createPlusOrder(event);
            // 划扣续费单
            PlusDeductEvent deductEvent = converter.toPlusDeductEvent(renewInfo, "会员续费",
                    plusOrder.getOrderSn());
            this.deduct(deductEvent);
        } catch (Exception e) {
            LogUtil.printLog(e, "还款卡续费异常");
            imRepository.sendImMessage(
                    e instanceof PlusAbyssException ? "还款卡续费异常,用户id：" + userId + ",原因："
                            + e.getMessage() : "还款卡续费异常,用户id：" + userId);
        }
    }

    @Override
    public void payResultCallback(PayCallbackEntity entity) {
        // 处理支付记录（目前只有888渠道使用）
        if (StringUtils.equals(entity.getPayType(), PayTypeEnum.DEDUCT.getCode())
                && StringUtils.equals(entity.getStatus(), PayStateEnum.F.getCode())) {
            payRecordModel.payFail(entity);
        }
        // 支付成功
        if (StringUtils.equals(entity.getStatus(), PayStateEnum.S.getCode())) {
            // 处理分流实际入账方
            orderShuntModel.paySuccessInSupplier(entity);
        }
        // 划扣成功,三方预入账（整体流程：合同签署-签署结果回调-合同上传三方服务器-通知三方入账）
        if (StringUtils.equals(entity.getPayType(), PayTypeEnum.DEDUCT.getCode())
                && StringUtils.equals(entity.getStatus(), PayStateEnum.S.getCode())) {
            orderBillModel.incomeNotify(entity);
            // 支付记录处理为支付成功（目前只有888渠道在用）
            payRecordModel.paySuccess(entity);
        }
        // 小额月卡划扣失败处理
        try {
            if (StringUtils.equals(entity.getPayType(), PayTypeEnum.DEDUCT.getCode())
                    && StringUtils.equals(entity.getStatus(), PayStateEnum.F.getCode())) {
                String orderSn = entity.getOrderId();
                PlusOrderEntity order = plusOrderQueryModel.getByOrderSn(orderSn);
                if (order == null || order.getConfigId() != JuziPlusEnum.XEYK_CARD.getCode()) {
                    log.info("小额月卡划扣失败处理非会员单/非小额月卡订单：{}", order);
                    return;
                }
                // 获取续费计划
                MemberPlusRenewPlanEntity renewPlan = renewPlanQueryModel.getByPlusOrderSn(
                        entity.getOrderId());
                boolean firstOrder = renewPlan == null || renewPlan.getGroupId().equals(orderSn);
                if (firstOrder) {
                    log.info("小额月卡首单划扣失败处理：{}", orderSn);
                    // 无条件取消首单
                    PlusOrderCancelEvent cancelEvent = converter.toPlusOrderCancelEvent(order,
                            PlusCancelTypeEnum.NO_CONDITION.getValue(),
                            CancelReasonEnum.CANCEL_REASON_28.getCode(), 0, "system");
                    cancelPlusOrder(cancelEvent);
                    // 取消划扣计划
                    plusOrderDeductPlanModel.updDeductPlanState(orderSn,
                            DeductPlanStateEnum.INVALID.getCode());
                    return;
                }
                log.info("小额月卡续费单划扣失败处理：{}", orderSn);
                // 取消续费计划（不取消订单）
                CancelRenewEvent event = converter.toCancelRenewEvent(orderSn,
                        "续费单划扣失败自动取消续费", CommonConstant.THREE);
                cancelRenew(event);
                // 修改划扣计划=失败
                plusOrderDeductPlanModel.updDeductPlanState(orderSn,
                        DeductPlanStateEnum.DEDUCT_FAIL.getCode());
            }
        } catch (Exception e) {
            LogUtil.printLog(e, "处理小额月卡划扣失败异常");
        }
    }

    @Override
    public void orderRefundCallback(OrderCancelFirstPayEntity entity) {
        String orderSn = entity.getOrderId();
        if (StringUtils.isBlank(orderSn)) {
            log.info("订单退款业务回调处理订单号为空");
            return;
        }
        PlusOrderEntity plusOrderInfo = plusOrderQueryModel.getByOrderSn(orderSn);
        log.info("订单退款业务回调处理查询订单信息为:{}", JSON.toJSONString(plusOrderInfo));
        if (plusOrderInfo == null) {
            log.info("订单退款业务回调处理未查到订单信息：{}", orderSn);
            return;
        }
        //保存会员退款记录
        MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
        plusLog.memberId(plusOrderInfo.getUserId()).channelId(plusOrderInfo.getChannelId())
                .programId(plusOrderInfo.getProgramId()).orderSn(orderSn)
                .optId(CommonConstant.STRING_SYSTEM).optName(CommonConstant.OPERATPR_NAME);
        plusLog.setNodeType(LogNodeEnum.LOG_NODE_ORDER_CANCEL_FIRST_PAY.getCode());
        String remark = entity.getRemark();
        plusLog.setRemark(StringUtils.isBlank(remark) ? "正常退款" : remark);
        logRepository.saveMemberPlusLog(plusLog);
    }

    @Override
    public void resubmitFlagVerify() {
        plusOrderModel.resubmitFlagVerify();
    }

    @Override
    public PlusOrderCancelAo cancelOrderApply(PlusOrderCancelEvent event) {
        // 校验参数
        PlusOrderEntity plusOrder = plusOrderValidator.cancelOrderApplyValidator(event);
        // 20240907 zjf 清分：取消订单新老逻辑兼容
        if (!switchUtil.isNew(plusOrder.getUserId())) {
            log.info("原路退款取消非白名单用户走原逻辑：{}", plusOrder.getOrderSn());
            return springUtils.getBean(IPlusOrderApplication.class).cancelPlusOrder(event);
        }
        // 取消前校验
        PlusOrderCancelAo checkPreCancelResult = checkPreCancel(event);
        if (!checkPreCancelResult.isCanBeCancel()) {
            throw new PlusAbyssException(String.valueOf(checkPreCancelResult.getCode()),
                    checkPreCancelResult.getReason());
        }
        String plusOrderSn = event.getPlusOrderSn();
        // 退款业务流水号
        String refundSerialNo = SerialNoUtils.generateApplySerialNo(
                SerialNoPrefixConstant.SERIAL_NO_PREFIX_YLT, plusOrderSn);
        // 保存订单退款申请信息
        PlusOrderRefundInfoEntity refundInfoEntity = converter.toPlusOrderRefundInfoEntity(event,
                refundSerialNo, RefundInfoStateEnum.DOING.getCode(), RefundTypeEnum.YLT.getCode());

        if (Objects.equals(plusOrder.getPayType(), PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())
                && Objects.equals(plusOrder.getOrderState(), PlusOrderStateEnum.PAY_SUCCESS.getCode())) {
            //走新的逻辑，对退款金额进行折分
            return this.cancelFirstPayOrder(event, checkPreCancelResult, plusOrder, refundInfoEntity,
                    checkPreCancelResult.getMoneyBack());
        }
        Long id = orderRefundInfoModel.saveOrderRefundInfo(refundInfoEntity);
        String tradeSubject =
                JuziPlusEnum.RDZX_CARD.getCode() == plusOrder.getConfigId() ? RDZX_SUBJECT
                        : SUBJECT;
        // 调订单中心取消订单
        OrderCancelRefundVO vo = buildOrderCancelParam(plusOrderSn, event,
                checkPreCancelResult.getMoneyBack(), plusOrder.getOrderAmount(), refundSerialNo,
                tradeSubject, plusOrder.getChannelId());
        OrderCancelRefundResultVO result = iOrderExternalRepository.closeOrderRefund(vo);
        if (result == null) {
            throw new PlusAbyssException("原路退款,调订单中心取消订单异常");
        }
        // 调订单中心取消同步返回失败,修改订单退款信息状态
        if (PayStateCodeEnum.F.getCode().equals(result.getStatus())) {
            orderRefundInfoModel.updateRefundState(refundSerialNo,
                    RefundInfoStateEnum.FAIL.getCode());
        }
        // 更新支付退款流水号,调订单可能不会同步返回支付侧流水号
        String paySerialNo = result.getSerialNumber();
        if (StringUtils.isNotBlank(paySerialNo)) {
            orderRefundInfoModel.updatePaySerialNo(id, result.getSerialNumber());
        }
        // 记录发起退款申请成功日志
        saveCancelLog(event, plusOrder);
        //使用了加速权益，加入会员黑名单
        try {
            blackPlusUser(plusOrder);
        } catch (Exception e) {
            log.error("加入会员黑名单失败,uid：{}，orderSn：{}", plusOrder.getUserId(),plusOrder.getOrderSn(), e);
        }
        return checkPreCancelResult;
    }

    /**
     * 记录退款记录和明细
     */
    private PlusOrderCancelAo cancelFirstPayOrder(PlusOrderCancelEvent event, PlusOrderCancelAo checkPreCancelResult,
                                                  PlusOrderEntity orderEntity,
                                                  PlusOrderRefundInfoEntity plusOrderRefundInfoEntity, BigDecimal refundAmt) {
        //查询支付明细
        List<PlusOrderSeparateEntity> plusOrderSeparateList = plusOrderSeparateModel.getPlusOrderSeparate(orderEntity.getOrderSn());
        List<PlusOrderSeparateEntity> successRecord = plusOrderSeparateList.stream().filter(
                t -> Objects.equals(t.getSeparateState(), SeparateStateEnum.SUCCESS.getCode()))
                .sorted(Comparator.comparing(PlusOrderSeparateEntity::getTotalSeparateAmount).reversed())
                .collect(Collectors.toList());
        if (successRecord.size() != 2) {
            log.error("支付记录异常,orderSn:{}", orderEntity.getOrderSn());
            String alertMsg = String.format("首期支付订单退款记录异常,orderSn:%s", orderEntity.getOrderSn());
            imRepository.sendImMessage(alertMsg);
            throw new PlusAbyssException("会员取消-支付记录异常");
        }
        BigDecimal balanceRefundAmt =  refundAmt;
        List<PlusOrderRefundDetailEntity> detailEntities = Lists.newArrayList();
        int index = 1;
        for (PlusOrderSeparateEntity separateEntity : successRecord) {
            if (balanceRefundAmt.compareTo(BigDecimal.ZERO) > 0) {
                PlusOrderRefundDetailEntity detailEntity = new PlusOrderRefundDetailEntity();
                detailEntity.setSeparateId(separateEntity.getId());
                detailEntity.setRefundSerialNo(plusOrderRefundInfoEntity.getRefundSerialNo() + "P" + index);
                BigDecimal detailRefundAmt = balanceRefundAmt.compareTo(separateEntity.getTotalSeparateAmount()) > 0
                        ? separateEntity.getTotalSeparateAmount() : balanceRefundAmt;
                detailEntity.setRefundAmount(detailRefundAmt);
                detailEntity.setTotalRefundAmount(refundAmt);
                detailEntity.setCurrentPeriod(index);
                detailEntity.setRefundState(index == 1 ? RefundInfoStateEnum.DOING.getCode() : RefundInfoStateEnum.INIT.getCode());
                balanceRefundAmt = balanceRefundAmt.subtract(detailRefundAmt);
                detailEntities.add(detailEntity);
                index++;
            }
        }
        Long refundInfoId = orderRefundInfoModel.saveOrderRefundInfoAndDetail(plusOrderRefundInfoEntity, detailEntities);
        //发起第一笔退款
        PlusOrderRefundDetailEntity refundDetailEntity = detailEntities.get(0);
        // 调订单中心取消订单
        OrderCancelRefundVO vo = buildOrderDetailCancelParam(orderEntity, refundDetailEntity, event);
        OrderCancelRefundResultVO result = iOrderExternalRepository.closeOrderRefund(vo);
        if (result == null) {
            throw new PlusAbyssException("原路退款,调订单中心取消订单异常");
        }
        // 调订单中心取消同步返回失败,修改订单退款信息状态
        if (PayStateCodeEnum.F.getCode().equals(result.getStatus())) {
            orderRefundInfoModel.updateRefundInfoAndDetailState(refundInfoId,
                    detailEntities.stream().map(PlusOrderRefundDetailEntity::getId).collect(Collectors.toList()),
                    RefundInfoStateEnum.FAIL.getCode());
        }
        // 更新支付退款流水号,调订单可能不会同步返回支付侧流水号
        String paySerialNo = result.getSerialNumber();
        orderRefundInfoModel.updateRefundDetailBySerialNo(refundDetailEntity.getRefundSerialNo(), paySerialNo);
        // 记录发起退款申请成功日志
        saveCancelLog(event, orderEntity);
        //使用了加速权益，加入会员黑名单
        try {
            blackPlusUser(orderEntity);
        } catch (Exception e) {
            log.error("加入会员黑名单失败,uid：{}，orderSn：{}", orderEntity.getUserId(),orderEntity.getOrderSn(), e);
        }
        return checkPreCancelResult;
    }

    /**
     * 使用了加速权益，加入会员黑名单
     */
    private void blackPlusUser(PlusOrderEntity plusOrder) {
        Integer userId = plusOrder.getUserId();
        String orderSn = plusOrder.getOrderSn();
        log.info("使用加速权益，加入会员黑名单start, userId:{}, orderSn:{}", userId, orderSn);

        // 加速卡会员处理
        if (isExpediteCardMember(plusOrder)) {
            return;
        }

        // 普通会员处理
        handleNormalMember(plusOrder);
    }

    /**
     * 处理加速卡会员
     */
    private boolean isExpediteCardMember(PlusOrderEntity plusOrder) {
        if (plusOrder.getConfigId() != JuziPlusEnum.EXPEDITE_CARD.getCode()) {
            return false;
        }

        PlusUseProfitSuPo profitSuPo = memberPlusExclusiveRepository.selectByPlusOrderSn(plusOrder.getOrderSn());
        if (profitSuPo == null) {
            log.info("加速卡未使用加速权益, userId:{}, orderSn:{}", plusOrder.getUserId(), plusOrder.getOrderSn());
            return true;
        }

        if (plusBlackRepository.inBlackList(plusOrder.getUserId(), null, BlackListTypeEnum.BLACK_TYPE_2)) {
            log.info("会员已经加入会员黑名单,无需处理, userId:{}, orderSn:{}", plusOrder.getUserId(), plusOrder.getOrderSn());
            return true;
        }

        blackUser(plusOrder);
        return true;
    }

    /**
     * 处理普通会员
     */
    private void handleNormalMember(PlusOrderEntity plusOrder) {
        List<PlusProModelEntity> rights = plusOrderSnapshtoQueryModel.listOrderModelByOrderBySort(plusOrder);
        if (CollectionUtils.isEmpty(rights)) {
            log.info("会员无权益, userId:{}, orderSn:{}", plusOrder.getUserId(), plusOrder.getOrderSn());
            return;
        }

        // 过滤出加速权益
        List<PlusProModelEntity> speedUpRights = rights.stream()
                .filter(right -> PlusModelEnum.GKSH.getModelId().equals(right.getModelId())
                        || PlusModelEnum.GKFK.getModelId().equals(right.getModelId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(speedUpRights)) {
            log.info("会员无加速权益, userId:{}, orderSn:{}", plusOrder.getUserId(), plusOrder.getOrderSn());
            return;
        }

        MemberPlusInfoDetailEntity detailEntity = memberPlusInfoDetailRepository.getByOrderSn(plusOrder.getOrderSn());

        // 检查加速权益使用情况
        for (PlusProModelEntity right : speedUpRights) {
            Map<String, Object> dataMap = getRightUseInfo(right, detailEntity);
            if (isRightUsed(dataMap)) {
                handleBlackList(plusOrder);
                break;
            }
        }
    }

    /**
     * 判断权益是否已使用
     */
    private boolean isRightUsed(Map<String, Object> dataMap) {
        return dataMap != null && dataMap.get("isUse") != null && (Boolean) dataMap.get("isUse");
    }

    /**
     * 处理黑名单
     */
    private void handleBlackList(PlusOrderEntity plusOrder) {
        if (plusBlackRepository.inBlackList(plusOrder.getUserId(), null, BlackListTypeEnum.BLACK_TYPE_2)) {
            log.info("会员已经加入会员黑名单,无需处理, userId:{}, orderSn:{}", plusOrder.getUserId(), plusOrder.getOrderSn());
            return;
        }
        blackUser(plusOrder);
    }

    /**
     * 获取权益使用信息
     */
    private Map<String, Object> getRightUseInfo(PlusProModelEntity right, MemberPlusInfoDetailEntity detailEntity) {
        QueryProfitUsedEvent event = new QueryProfitUsedEvent();
        event.setOrderSn(detailEntity.getOrderSn());
        event.setProgramId(detailEntity.getProgramId());
        event.setChannel(detailEntity.getChannelId());
        event.setUserId(detailEntity.getUserId());
        event.setConfigId(detailEntity.getConfigId());
        event.setJxStartTime(detailEntity.getJxStartTime());
        event.setJxEndTime(detailEntity.getJxEndTime());
        event.setModelId(right.getModelId());
        return profitHandlerContext.getProfitUsedInfo(event);
    }

    /**
     * 加入会员黑名单
     */
    private void blackUser(PlusOrderEntity plusOrder) {
        log.info("使用加速权益(更快审核/更快放款)加入会员黑名单, userId:{}, orderSn:{}", plusOrder.getUserId(), plusOrder.getOrderSn());
        PlusMemberBlackEntity black = new PlusMemberBlackEntity();
        black.setBlackType(BlackListTypeEnum.BLACK_TYPE_2.getCode());
        black.setChannelId(plusOrder.getChannelId());
        black.setConfigId(plusOrder.getConfigId());
        black.setUserId(plusOrder.getUserId());
        plusBlackRepository.saveBlackRecord(black);
    }

    /**
     * 记录发起退款申请成功日志
     */
    private void saveCancelLog(PlusOrderCancelEvent event, PlusOrderEntity plusOrder) {
        MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
        plusLog.memberId(event.getUserId()).channelId(event.getChannelId())
                .programId(event.getProgramId()).orderSn(event.getPlusOrderSn())
                .reason(event.getCancelReason())
                .cancelRemark("取消发起成功," + plusOrderModel.getCancelRemark(plusOrder, event))
                .optId(event.getOptUserId()).optName(event.getOptUserName())
                .useProfit(event.getUseProfit());
        logRepository.saveCancelLogByUser(plusLog, plusOrderModel.getCancelOrderNode(event));
    }

    /**
     * 构建调订单中心取消订单参数
     */
    private OrderCancelRefundVO buildOrderCancelParam(String plusOrderSn,
            PlusOrderCancelEvent event, BigDecimal moneyBack, BigDecimal orderAmount,
            String refundSerialNo, String tradeSubject, Integer channelId) {
        OrderCancelRefundVO vo = new OrderCancelRefundVO();
        vo.setOrderSn(plusOrderSn);
        vo.setCancelReason(CancelReasonEnum.getNameByCode(event.getCancelReason()));
        vo.setOperatingId(event.getOptUserId());
        vo.setOperatingName(event.getOptUserName());
        // 退款信息
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setRefundType(PayRefundTypeEnum.ORIGINAL.getCode());
        // 是否部分退款
        int partRefund =
                moneyBack.compareTo(BigDecimal.ZERO) > 0 && moneyBack.compareTo(orderAmount) < 0 ? 1
                        : 0;
        refundInfo.setPartRefund(partRefund);
        refundInfo.setApplication(String.valueOf(channelId));
        refundInfo.setSource(PaySourceConstant.PAY_SOURCE_MEMBER);
        refundInfo.setRefundAmount(moneyBack);
        refundInfo.setThirdPayNum(refundSerialNo);
        refundInfo.setPayProductCode(PayProductCodeEnum.TK.getCode());
        // 交易摘要
        refundInfo.setTradeSubject(tradeSubject);
        // 业务场景
        String businessScene = shuntRepository.getBusinessScene(plusOrderSn);
        refundInfo.setBusinessScene(businessScene);
        // 扩展信息
        refundInfo.setExtInfo(JSONObject.toJSONString(event.getExtInfo()));
        vo.setRefundInfo(refundInfo);
        return vo;
    }

    private OrderCancelRefundVO buildOrderDetailCancelParam(PlusOrderEntity orderEntity,
                                                            PlusOrderRefundDetailEntity detailEntity,
                                                            PlusOrderCancelEvent event) {
        OrderCancelRefundVO vo = new OrderCancelRefundVO();
        vo.setOrderSn(orderEntity.getOrderSn());
        vo.setCancelReason(CancelReasonEnum.getNameByCode(event.getCancelReason()));
        vo.setOperatingId(event.getOptUserId());
        vo.setOperatingName(event.getOptUserName());
        // 退款信息
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setRefundType(PayRefundTypeEnum.ORIGINAL.getCode());

        refundInfo.setPartRefund(1);
        refundInfo.setApplication(String.valueOf(orderEntity.getChannelId()));
        refundInfo.setSource(PaySourceConstant.PAY_SOURCE_MEMBER);
        refundInfo.setRefundAmount(detailEntity.getRefundAmount());
        refundInfo.setThirdPayNum(detailEntity.getRefundSerialNo());
        refundInfo.setPayProductCode(PayProductCodeEnum.TK.getCode());

        PlusOrderSeparateEntity separateEntity = plusOrderSeparateModel.getById(detailEntity.getSeparateId());
        String payNum = Objects.equals(separateEntity.getOrderPayAction(), 1) ?
                separateEntity.getSerialNo() : separateEntity.getPaySerialNo();
        refundInfo.setCancelPeriod(detailEntity.getTotalPeriod());
        refundInfo.setRefundTotalAmount(detailEntity.getTotalRefundAmount());
        refundInfo.setPayNum(payNum);
        // 交易摘要
        refundInfo.setTradeSubject(SUBJECT);
        String businessScene = shuntRepository.getBusinessScene(orderEntity.getOrderSn());
        refundInfo.setBusinessScene(businessScene);
        // 扩展信息
        refundInfo.setExtInfo(JSONObject.toJSONString(event.getExtInfo()));
        vo.setRefundInfo(refundInfo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderRefundNotify(OrderRefundNotifyEntity entity) {
        // 参数校验
        plusOrderValidator.orderRefundNotifyValidator(entity);
        // 根据退款方式处理
        if (PayRefundTypeEnum.CHANGE_CARD.getCode().equals(entity.getRefundType())) {
            // 换卡代付退款处理
            orderDefrayApplication.changeCardDefrayPayRefund(entity);
        } else {
            // 原路退款、原路换卡退款处理
            orderRefundInfoApplication.originalRefund(entity);
        }
    }

    @Override
    public void orderSecondRefund(OrderRefundSecondEvent event)  {
        if (PayRefundTypeEnum.CHANGE_CARD.getCode().equals(event.getRefundType())) {
            // 换卡代付第二次退款处理
            orderDefrayApplication.defrayPayRefundSecond(event);
        } else {
            // 原路退款第二次退款处理
            orderRefundInfoApplication.secondRefund(event);
        }
    }

    @Override
    public void newPayResultCallback(NewPayResultCallbackEntity entity) {
        if (entity == null || StringUtils.isBlank(entity.getPayProductCode())
                || StringUtils.isBlank(entity.getOrderId()) || StringUtils.isBlank(
                entity.getThirdPayNum()) || StringUtils.isBlank(entity.getState())
                || entity.getAmount() == null) {
            throw new PlusAbyssException("新支付系统回调结果数据异常");
        }

        // 主动支付成功地保存订单对账信息，划扣的是在划扣完成后进行保存的
        // 保存的对账信息会在后面的处理中进行状态更新
        if (isActivePaymentSuccessful(entity)) {
            PlusOrderEntity byPlusOrderSn = plusOrderQueryModel.getByOrderSn(entity.getOrderId());
            CreateOrderBillEvent createOrderBillEvent = converter.toCreateOrderBillEvent(byPlusOrderSn);
            if (PayStateCodeEnum.S.getCode().equals(entity.getState())) {
                createOrderBillEvent.setSerialNumber(entity.getSerialNumber());
            }
            orderBillModel.saveOrderBill(createOrderBillEvent);
        }

        // 处理支付记录（目前只有888渠道使用）
        if (PayProductCodeEnum.HK.getCode().equals(entity.getPayProductCode())
                && PayStateCodeEnum.F.getCode().equals(entity.getState())) {
            payRecordModel.newPayFail(entity);
        }
        // 支付成功
        if (PayStateCodeEnum.S.getCode().equals(entity.getState())) {
            // 处理分流实际入账方
            orderShuntModel.newPaySuccessInSupplier(entity);
        }
        // 划扣成功,三方预入账（整体流程：合同签署-签署结果回调-合同上传三方服务器-通知三方入账）
        if (PayProductCodeEnum.HK.getCode().equals(entity.getPayProductCode())
                && PayStateCodeEnum.S.getCode().equals(entity.getState())) {
            orderBillModel.newPayIncomeNotify(entity);
            // 支付记录处理为支付成功（目前只有888渠道在用）
            payRecordModel.newPaySuccess(entity);
        } else if (isActivePaymentSuccessful(entity)) {
            // 更新对账信息
            orderBillModel.newPayIncomeNotify(entity);
        }
        // 小额月卡划扣失败处理
        xeykPayNotifyProcess(entity);
        // 处理分账记录
        separateModel.payNotify(entity);
    }

    /**
     * 判断是否主动支付成功
     *
     * @param entity 新支付系统支付结果回调
     * @return true：是 false：否
     */
    private boolean isActivePaymentSuccessful(NewPayResultCallbackEntity entity) {
        return PayStateCodeEnum.S.getCode().equals(entity.getState()) && isActivePayment(entity);
    }

    /**
     * 判断是否为主动支付
     *
     * @param entity 新支付系统支付结果回调
     * @return true：是 false：否
     */
    private boolean isActivePayment(NewPayResultCallbackEntity entity) {
        return Arrays.asList(PayProductCodeEnum.ZFBZF.getCode(), PayProductCodeEnum.ZXZF.getCode())
                .contains(entity.getPayProductCode());
    }

    /**
     * 小额月卡划扣失败处理
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/9 10:41
     */
    private void xeykPayNotifyProcess(NewPayResultCallbackEntity entity) {
        try {
            if (PayProductCodeEnum.HK.getCode().equals(entity.getPayProductCode())
                    && PayStateCodeEnum.F.getCode().equals(entity.getState())) {
                String orderSn = entity.getOrderId();
                PlusOrderEntity order = plusOrderQueryModel.getByOrderSn(orderSn);
                if (order == null || order.getConfigId() != JuziPlusEnum.XEYK_CARD.getCode()) {
                    log.info("新支付系统小额月卡划扣失败处理非会员单/非小额月卡订单：{}", order);
                    return;
                }
                // 获取续费计划
                MemberPlusRenewPlanEntity renewPlan = renewPlanQueryModel.getByPlusOrderSn(
                        entity.getOrderId());
                boolean firstOrder = renewPlan == null || renewPlan.getGroupId().equals(orderSn);
                if (firstOrder) {
                    log.info("新支付系统小额月卡首单划扣失败处理：{}", orderSn);
                    // 无条件取消首单
                    PlusOrderCancelEvent cancelEvent = converter.toPlusOrderCancelEvent(order,
                            PlusCancelTypeEnum.NO_CONDITION.getValue(),
                            CancelReasonEnum.CANCEL_REASON_28.getCode(), 0, "system");
                    IPlusOrderApplication orderApplication = springUtils.getBean(
                            IPlusOrderApplication.class);
                    orderApplication.cancelOrderApply(cancelEvent);
                    return;
                }
                log.info("新支付系统小额月卡续费单划扣失败处理：{}", orderSn);
                // 取消续费计划（不取消订单）
                CancelRenewEvent event = converter.toCancelRenewEvent(orderSn,
                        "新支付系统续费单划扣失败自动取消续费", CommonConstant.THREE);
                cancelRenew(event);
                // 修改划扣计划=失败
                plusOrderDeductPlanModel.updDeductPlanState(orderSn,
                        DeductPlanStateEnum.DEDUCT_FAIL.getCode());
            }
        } catch (Exception e) {
            LogUtil.printLog(e, "新支付系统处理小额月卡划扣失败异常");
        }
    }

    @Override
    public PlusOrderPayInfoAo getOrderPayInfo(String orderSn) {
        log.info("开始生成请求支付信息 orderSn {}", orderSn);
        if (StringUtils.isBlank(orderSn)) {
            log.info("请求的订单不存在 orderSn {}", orderSn);
            throw new PlusAbyssException("会员订单号不能为空!");
        }
        PlusOrderEntity plusOrderEntity = plusOrderQueryModel.getByOrderSn(orderSn);
        if (Objects.isNull(plusOrderEntity)) {
            log.info("请求的订单不存在 orderSn {}", orderSn);
            throw new PlusAbyssException("请求的订单不存在!");
        }
        if (!Objects.equals(PlusOrderStateEnum.WAIT_PAY.getCode(),
                plusOrderEntity.getOrderState())) {
            log.info("请求的订单非待支付状态，不返回支付信息 orderSn {} orderState {}", orderSn,
                    plusOrderEntity.getOrderState());
            throw new PlusAbyssException("请求的订单非待支付状态");
        }
        //如果是后付款或者分期支付订单,加锁防止主动支付和代扣导致用户重复支付的问题
        boolean flag = Objects.equals(plusOrderEntity.getPayType(), PlusOrderPayTypeEnum.PAY_AFTER.getValue()) || Objects.equals(plusOrderEntity.getPayType(), PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue());
        if (flag && redisUtils.hasKey(PLUS_ORDER_AFTER_HK + orderSn)) {
            log.info("会员订单系统划扣中 orderSn:{}", orderSn);
            throw new PlusAbyssException("9999", "您的会员订单正在支付中，请勿重复支付");
        }
        // 加锁防止重复生成分账信息
        String lockKey = PlusConcatUtils.symbolBelowStr(
                RedisConstantPrefix.PLUS_ORDER_QUERY_PAY_INFO, orderSn);
        RLock rLock = redissonClient.getLock(lockKey);
        PlusOrderPayInfoAo plusOrderPayInfoAo;
        try {
            boolean result = rLock.tryLock(2, TimeUnit.SECONDS);
            if (!result) {
                throw new PlusAbyssException("请求订单支付信息,加锁失败");
            }
            // 构建订单支付信息返回
            plusOrderPayInfoAo = buildOrderPayInfo(orderSn, plusOrderEntity);
        } catch (Exception e) {
            log.info("请求订单支付信息异常", e);
            if (e instanceof PlusAbyssException) {
                throw new PlusAbyssException(e.getMessage());
            }
            throw new RuntimeException(e);
        } finally {
            if (rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
        return plusOrderPayInfoAo;
    }

    @Override
    public void resetDuctPlan(String orderNoStr, Integer configId, Integer programId) {
        List<PlusOrderEntity> toDealPlusOrder = new ArrayList<>();
        //校验订单是否支付
        if (!StringUtils.isBlank(orderNoStr)) {
            List<String> orderSnList = Arrays.stream(orderNoStr.split(",")).collect(Collectors.toList());
            toDealPlusOrder = plusOrderQueryModel.getWaitPayOrderList(orderSnList);
        } else {
            if (configId == null || programId == null) {
                //订单号时，会员类型和方案id不能为空
                return;
            }
            PlusOrderQueryReq req = new PlusOrderQueryReq();
            req.setConfigId(configId);
            req.setProgramId(programId);
            toDealPlusOrder = plusOrderQueryModel.getUserWaitPayOrderList(req);
        }
        //校验代扣失败并且次数达到限制
        for (PlusOrderEntity orderEntity : toDealPlusOrder) {
            PlusOrderDeductPlanEntity delayDeductPlan =
                    plusOrderDeductPlanModel.getDelayDeductByPlusOrder(orderEntity.getOrderSn());
            if (delayDeductPlan != null && delayDeductPlan.getDeductNum() >= 6
                    && delayDeductPlan.getOptStatus() == 3) {
                //重置次数
                delayDeductPlan.setOptStatus(0);
                delayDeductPlan.setDeductNum(5);
                delayDeductPlan.setPlanTime(LocalDateTimeUtils.dateAfterMin(new Date(), 10));
                plusOrderDeductPlanModel.updateDelayDeductPlan(delayDeductPlan);
            }
        }
    }

    @Override
    public void retryOrderSecondRefund(Long refundInfoId) {
        PlusOrderRefundInfoEntity refundInfoEntity = orderRefundInfoModel.getById(refundInfoId);
        if (refundInfoEntity == null || !Objects.equals(refundInfoEntity.getRefundState(), RefundInfoStateEnum.DOING.getCode())) {
            return;
        }
        PastMemberRefundRecordEntity pastMemberRefundRecord = pastMemberRefundRecordRepository
                .getByDefraySerialNo(refundInfoEntity.getRefundSerialNo());
        if (Objects.equals(refundInfoEntity.getRefundType(), RefundTypeEnum.DFTK.getCode())
                && pastMemberRefundRecord != null) {
            log.info("代付打款记录不存在, orderSn:{}", refundInfoEntity.getOrderSn());
            return;
        }
        List<PlusOrderRefundDetailEntity> detailEntityList = orderRefundInfoModel.getDetailsByInfoId(refundInfoId);
        if (CollectionUtils.isEmpty(detailEntityList)) {
            return;
        }
        List<PlusOrderRefundDetailEntity> secondFailList = detailEntityList.stream()
                .filter(t -> Objects.equals(t.getCurrentPeriod(), t.getTotalPeriod()))
                .sorted(Comparator.comparing(PlusOrderRefundDetailEntity::getId))
                .collect(Collectors.toList());
        if (secondFailList.stream().allMatch(t ->
                Objects.equals(t.getRefundState(), RefundInfoStateEnum.FAIL.getCode()))) {
            log.info("第二期退款明细非全部失败，不需要重试, refundInfoId:{}", refundInfoId);
            return;
        }
        PlusOrderRefundDetailEntity plusOrderRefundDetailEntity = secondFailList.get(0);
        String refundSerial = plusOrderRefundDetailEntity.getRefundSerialNo().split("P")[0] + "P" + (secondFailList.size() + 2);
        PlusOrderRefundDetailEntity newRefundDetailEntity = new PlusOrderRefundDetailEntity();
        newRefundDetailEntity.setRefundInfoId(refundInfoId);
        newRefundDetailEntity.setSeparateId(plusOrderRefundDetailEntity.getSeparateId());
        newRefundDetailEntity.setRefundSerialNo(refundSerial);
        newRefundDetailEntity.setRefundState(RefundInfoStateEnum.INIT.getCode());
        orderRefundInfoModel.saveOrderRefundDetail(newRefundDetailEntity);

        OrderRefundSecondEvent  event = new OrderRefundSecondEvent();
        event.setOrderSn(refundInfoEntity.getOrderSn());
        event.setRefundType(refundInfoEntity.getRefundType());
        if (pastMemberRefundRecord != null) {
            event.setPastRecordId(pastMemberRefundRecord.getId());
        }
        event.setRefundDetailId(newRefundDetailEntity.getId());
        this.orderSecondRefund(event);
    }

    /**
     * 构建订单支付信息返回
     */
    private PlusOrderPayInfoAo buildOrderPayInfo(String orderSn, PlusOrderEntity plusOrderEntity) {
        //如果是后付款订单,加锁防止主动支付和代扣导致用户重复支付的问题
        if (Objects.equals(plusOrderEntity.getPayType(), PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
            log.info("后付款订单主动支付加缓存标识,orderSn:{}", orderSn);
            redisUtils.setEx(PLUS_ORDER_AFTER_ZD + orderSn, "1",
                    configProperties.afterPayLockTime, TimeUnit.SECONDS);
        }
        // 缓存中查询订单支付信息
        String key = PlusConcatUtils.symbolBelowStr(PLUS_ORDER_PAY_INFO, orderSn);
        String result = redisUtils.get(key);
        if (StringUtils.isNotBlank(result)) {
            log.info("缓存中查询订单支付信息,{}", orderSn);
            return JSONObject.parseObject(result, PlusOrderPayInfoAo.class);
        }
        // 判断是否为分期支付
        boolean isInstallmentPayment = PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue()
                .equals(plusOrderEntity.getPayType());

        // 获取支付分账信息
        PlusOrderDeductResEntity getResEntity;
        PlusDeductEvent deductPlan = converter.toPlusDeductEvent(plusOrderEntity, "会员主动支付", OrderPayActionEnum.PAY_ACTION_1);
        if (isInstallmentPayment) {
            getResEntity = separateModel.getInstallmentPaymentSeparateInfo(plusOrderEntity, deductPlan);
        } else {
            getResEntity = separateModel.getPlusOrderSeparateInfo(plusOrderEntity, deductPlan);
        }
        if (!DeductResultStateEnum.SUCCESS.getCode().equals(getResEntity.getOptStatus())) {
            log.info("获取主动支付分账信息失败orderSn {} separateResult {}", orderSn,
                    JSONObject.toJSON(deductPlan));
            throw new PlusAbyssException("获取订单支付信息失败");
        }
        PlusOrderSeparateEntity separateEntity = getResEntity.getSeparateEntity();
        PlusOrderPayInfoAo plusOrderPayInfoAo = new PlusOrderPayInfoAo();
        plusOrderPayInfoAo.setUserId(String.valueOf(separateEntity.getUserId()));
        plusOrderPayInfoAo.setApplication(String.valueOf(plusOrderEntity.getChannelId()));
        plusOrderPayInfoAo.setOrderSn(plusOrderEntity.getOrderSn());
        plusOrderPayInfoAo.setConfigId(plusOrderEntity.getConfigId());
        // 普通支付：使用订单总金额
        plusOrderPayInfoAo.setTotalAmount(separateEntity.getTotalSeparateAmount());
        plusOrderPayInfoAo.setTradeName(deductPlan.getTradeName());
        plusOrderPayInfoAo.setSource(PaySourceConstant.PAY_SOURCE_MEMBER);
        plusOrderPayInfoAo.setBusinessScene(separateEntity.getBusinessScene());
        plusOrderPayInfoAo.setApplySerialNo(separateEntity.getApplySerialNo());
        // 会员
        plusOrderPayInfoAo.setTradeType("R");
        if (!CollectionUtils.isEmpty(separateEntity.getItems())) {
            plusOrderPayInfoAo.setSplitInfo(new ArrayList<>());
            separateEntity.getItems().forEach(item -> {
                // 只放清分主体
                if (item.getSupplierType().equals(SupplierTypeEnum.QF.getCode())) {
                    DivideInfo divideInfo = new DivideInfo();
                    divideInfo.setCode(item.getMerchantId());
                    divideInfo.setAmount(item.getSeparateAmount());
                    plusOrderPayInfoAo.getSplitInfo().add(divideInfo);
                }
            });
        }
        // 设置支付信息缓存
        redisUtils.setEx(key, JSONObject.toJSONString(plusOrderPayInfoAo), 5, TimeUnit.SECONDS);
        log.info("结束生成请求支付信息 {}", JSONObject.toJSONString(plusOrderPayInfoAo));
        return plusOrderPayInfoAo;
    }




}
