package com.juzifenqi.plus.module.market.model.contract.entity;

import com.juzifenqi.plus.enums.market.ShowTypeEnum;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierContractEntity;
import java.math.BigDecimal;
import java.util.List;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 信用支付成功页会员营销返回结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/22 17:08
 */
@Data
@Accessors(chain = true)
public class PlusPaySuccessMarketEntity {


    /**
     * 0=不营销任何卡 12=小额月卡  3=加速卡，此字段含义与借款首页、确认借款页此字段含义一致，默认不营销
     *
     * @see ShowTypeEnum
     */
    private Integer showType = ShowTypeEnum.NO_SHOW.getCode();

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 营销的方案id
     */
    private Integer programId;

    /**
     * 分流合作方id
     * <p>营销卡的前提下才可能返回</p>
     */
    private Integer shuntSupplierId;

    /**
     * 分流主体名称
     */
    private String supplierName;

    /**
     * 分流主体合同列表
     */
    private List<PlusShuntSupplierContractEntity> contractList;

    /**
     * 方案价格
     */
    private BigDecimal mallMobilePrice;

    /**
     * 样式预留字段
     */
    private String styleCode;

    /**
     * 是否支持后付款 1支持 2不支持
     */
    private Integer afterPayState;

    /**
     * 会员权益
     */
    private List<PlusModelEntity> plusRightsList;

    /**
     * 不营销原因
     */
    private String noMarketReason;

    private List<Integer> payTypes;

    private BigDecimal firstPayAmount;

}
