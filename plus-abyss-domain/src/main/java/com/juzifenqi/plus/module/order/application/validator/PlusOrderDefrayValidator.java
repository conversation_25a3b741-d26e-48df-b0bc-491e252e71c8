package com.juzifenqi.plus.module.order.application.validator;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.aplha.bean.constant.CancelSceneConstant;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.enums.OptUserTypeEnum;
import com.juzifenqi.plus.enums.PlusCancelTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.enums.refund.RefundInfoStateEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.common.IAcmExternalRepository;
import com.juzifenqi.plus.module.order.application.converter.IPlusOrderDefrayApplicationConverter;
import com.juzifenqi.plus.module.order.model.IPlusOrderRefundInfoModel;
import com.juzifenqi.plus.module.order.model.PlusOrderModel;
import com.juzifenqi.plus.module.order.model.PlusOrderQueryModel;
import com.juzifenqi.plus.module.order.model.contract.IPastMemberRefundRecordRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderCancelEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IAlphaExternalRepository;
import com.juzifenqi.plus.module.order.model.event.order.CreateDefrayApplyEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.utils.ParamCheckUtils;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.yikoudai.zijin.cove.open.bill.resp.PlanSummaryResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单代付校验器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 14:34
 */
@Component
@Slf4j
public class PlusOrderDefrayValidator {

    private final IPlusOrderDefrayApplicationConverter converter = IPlusOrderDefrayApplicationConverter.instance;

    @Autowired
    private IPastMemberRefundRecordRepository repository;
    @Autowired
    private PlusOrderQueryModel               plusOrderQueryModel;
    @Autowired
    private IAlphaExternalRepository          alphaExternalRepository;
    @Autowired
    private PlusOrderModel            plusOrderModel;
    @Autowired
    private IPlusOrderRefundInfoModel orderRefundInfoModel;
    @Autowired
    private IAcmExternalRepository acmExternalRepository;
    @Autowired
    private ConfigProperties configProperties;

    /**
     * 创建代付申请校验
     */
    public PlusOrderEntity createDefrayApplyValidator(CreateDefrayApplyEvent event) {
        log.info("创建代付申请校验开始：{}", JSON.toJSONString(event));
        ParamCheckUtils.checkNull(event, "参数不能为空");
        ParamCheckUtils.checkNull(event.getOrderSn(), "会员单号不能为空");
        ParamCheckUtils.checkNull(event.getCancelReason(), "取消原因不能为空");
        ParamCheckUtils.checkNull(event.getRatio(), "退款比例不能为空");
        ParamCheckUtils.checkNull(event.getCustomerName(), "用户姓名不能为空");
        ParamCheckUtils.checkNull(event.getCtCardNo(), "银行卡号不能为空");
        ParamCheckUtils.checkNull(event.getCardId(), "银行卡id不能为空");
        ParamCheckUtils.checkNull(event.getOptId(), "操作人id不能为空");
        ParamCheckUtils.checkNull(event.getOptName(), "操作人姓名不能为空");
        // 退款比例范围
        if (event.getRatio().compareTo(BigDecimal.ZERO) < 0
                || event.getRatio().compareTo(BigDecimal.ONE) > 0) {
            throw new PlusAbyssException("退款比例范围必须在0%-100%");
        }
        String orderSn = event.getOrderSn();
        boolean hasOrder = repository.hasIngEnd(orderSn);
        if (hasOrder) {
            throw new PlusAbyssException("当前订单退款中，不可再次操作");
        }
        // 退款记录校验
        String plusOrderSn = event.getOrderSn();
        List<PlusOrderRefundInfoEntity> refundInfoList = orderRefundInfoModel.getByOrderSn(
                plusOrderSn, Arrays.asList(RefundInfoStateEnum.DOING.getCode(),
                        RefundInfoStateEnum.SUCCESS.getCode()));
        if (CollectionUtils.isNotEmpty(refundInfoList)) {
            throw new PlusAbyssException("订单存在已退款/退款中的退款记录,不可再次操作");
        }
        PlusOrderEntity plusOrder = plusOrderQueryModel.getByOrderSn(orderSn);
        ParamCheckUtils.checkNull(plusOrder, "当前订单不存在");
        PlusOrderCancelEvent cancelEvent = converter.toPlusOrderCancelEvent(event, plusOrder);
        //后台操作取消需要校验逾期
        if (Objects.equals(event.getOptUserType(), OptUserTypeEnum.ADMIN.getUserType())) {
            //逾期校验
            try {
                PlanSummaryResp planSummaryResp = acmExternalRepository.
                        queryRepayPlanSummary(plusOrder.getChannelId(), plusOrder.getUserId());
                if (planSummaryResp.getOverdueBillAmount() != null
                        && planSummaryResp.getOverdueBillAmount().compareTo(BigDecimal.ZERO) > 0) {
                    //判断当前操作用户是否为可操作用户
                    if (Arrays.stream(configProperties.overdueCancelUsers.split(",")).
                            noneMatch(t -> t.equals(event.getOptId().toString()))) {
                        throw new PlusAbyssException("用户有逾期的账单，无法进行会员退费");
                    }
                }
            } catch (Exception e) {
                log.error("会员取消查询用户逾期账单异常", e);
                throw new PlusAbyssException("会员取消查询用户逾期账单异常");
            }
        }
        // 过期取消
        if (event.getCancelType() == PlusCancelTypeEnum.EXPIRE_PAYMENT.getValue()) {
            log.info("过期取消校验：{}", orderSn);
            //当前订单是否为支付成功状态
            boolean isFirstPeriodPay = Objects.equals(plusOrder.getPayType(), PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue());
            if (!isFirstPeriodPay && plusOrder.getOrderState() != PlusOrderStateEnum.PAY_SUCCESS.getCode()) {
                throw new PlusAbyssException("当前订单不是支付成功状态");
            }
            if (isFirstPeriodPay && (plusOrder.getPayAmount() == null ||
                    plusOrder.getPayAmount().compareTo(BigDecimal.ZERO) <= 0)) {
                throw new PlusAbyssException("当前订单未支付过");
            }
            //当前订单对应的会员是否过期
            if (new Date().compareTo(plusOrder.getEndTime()) < 0) {
                throw new PlusAbyssException("当前订单对应的会员尚未过期");
            }
        } else {
            log.info("未过期取消规则校验：{}", orderSn);
            // 无条件取消校验
            alphaExternalRepository.cancelPreCheck(cancelEvent, CancelSceneConstant.WTJ);
        }
        // 计算扣减权益差价
        PlusOrderCancelEntity cancelEntity = plusOrderModel.calOrderRefundAmount(plusOrder,
                cancelEvent);
        if (!cancelEntity.isCanBeCancel()) {
            throw new PlusAbyssException(cancelEntity.getReason());
        }
        BigDecimal orderAmount = cancelEntity.getMoneyBack();
        // 退款金额是是否大于会员实付金额
        BigDecimal backMoney = event.getRatio().multiply(orderAmount);
        if (backMoney.compareTo(orderAmount) > 0) {
            throw new PlusAbyssException("退款金额不能大于会员实付金额");
        }
        return plusOrder;
    }
}
