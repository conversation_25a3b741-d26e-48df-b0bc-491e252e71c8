package com.juzifenqi.plus.module.order.model;

import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import java.util.List;

/**
 * 会员订单退款信息
 *
 * <AUTHOR>
 * @date 2024/9/2 11:20
 */
public interface IPlusOrderRefundInfoModel {

    /**
     * 根据订单号、状态查询
     */
    List<PlusOrderRefundInfoEntity> getByOrderSn(String orderSn, List<Integer> refundStateList);

    /**
     * 保存订单退款信息
     */
    Long saveOrderRefundInfo(PlusOrderRefundInfoEntity entity);


    Long saveOrderRefundInfoAndDetail(PlusOrderRefundInfoEntity entity, List<PlusOrderRefundDetailEntity> details);


    void saveOrderRefundDetail(PlusOrderRefundDetailEntity entity);
    /**
     * 根据主键id进行查询
     */
    PlusOrderRefundInfoEntity getById(Long refundInfoId);
    /**
     * 根据退款业务流水号查询
     */
    PlusOrderRefundInfoEntity getByRefundSerialNo(String refundSerialNo);

    PlusOrderRefundDetailEntity getDetailById(Long refundDetailId);

    PlusOrderRefundDetailEntity getDetailByRefundSerialNo(String refundSerialNo);


    List<PlusOrderRefundDetailEntity> getDetailsByInfoId(Long refundInfoId);

    /**
     * 修改退款状态
     */
    void updateRefundState(String refundSerialNo, Integer refundState);

    /**
     * 修改退款记录和退款明细状态
     */
    void updateRefundInfoAndDetailState(Long refundInfoId, List<Long> refundDetailIds, Integer refundState);


    void updateRefundDetailBySerialNo(String refundSerialNo,String paySerialNo);

    /**
     * 更新订单退款信息
     */
    void updateOrderRefundInfo(PlusOrderRefundInfoEntity entity);

    /**
     * 更新订单退款明细信息
     */
    void updateOrderRefundDetail(PlusOrderRefundDetailEntity entity);

    /**
     * 修改支付退款流水号
     */
    void updatePaySerialNo(String refundSerialNo, String paySerialNo);

    /**
     * 修改支付退款流水号
     */
    void updatePaySerialNo(Long id, String paySerialNo);

}
