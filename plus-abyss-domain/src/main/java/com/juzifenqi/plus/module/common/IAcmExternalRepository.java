package com.juzifenqi.plus.module.common;

import com.yikoudai.zijin.cove.open.bill.req.PlanQueryReq;
import com.yikoudai.zijin.cove.open.bill.resp.PlanSummaryResp;
import com.yikoudai.zijin.cove.open.info.RepayPlanInfo;

import java.util.List;

/**
 * 核心
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 13:55
 */
public interface IAcmExternalRepository {

    /**
     * 失效还款券
     */
    void invalidRepayCoupon(String couponNo);

    /**
     * 拉取还款计划
     */
    List<RepayPlanInfo> getRepayPlan(PlanQueryReq req);


    /**
     * 用户账单查询
     */
    PlanSummaryResp queryRepayPlanSummary(Integer channelId, Integer userId);

}
