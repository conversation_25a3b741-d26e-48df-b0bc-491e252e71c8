package com.juzifenqi.plus.module.settle.model.contract;

import com.juzifenqi.plus.dto.req.admin.settle.SettlePendingQueryReq;
import com.juzifenqi.plus.module.settle.model.contract.entity.PlusOrderSettleItemPendingAdminEntity;
import com.juzifenqi.plus.module.settle.model.contract.entity.PlusOrderSettleItemPendingEntity;
import java.util.List;

/**
 * 会员订单待结算明细表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/3 16:15
 */
public interface PlusOrderSettleItemPendingRepository {

    /**
     * 保存待结算明细
     */
    void saveOrderSettleItemPending(List<PlusOrderSettleItemPendingEntity> saveEntities);

    /**
     * 按日期查询待结算主体信息
     */
    List<PlusOrderSettleItemPendingEntity> getSettlePendingSupplierByDate(String beginTime,
            String endTime, Integer isCreateSettle);

    /**
     * 通过订单号查询待结算明细
     */
    List<PlusOrderSettleItemPendingEntity> getSettlePendingItemsByOrderSn(String orderSn);


    List<PlusOrderSettleItemPendingEntity> getByOrderSnAndSerialNo(String orderSn, String serialNo);

    /**
     * 按日期和主体查询待结算明细
     */
    List<PlusOrderSettleItemPendingEntity> getSettlePendingByDate(String beginTime, String endTime,
            Integer shuntSupplierId, Integer separateSupplierId, Integer isCreateSettle, Integer channelId);

    /**
     * 分页查询待结算列表
     */
    List<PlusOrderSettleItemPendingAdminEntity> getPendingPageList(SettlePendingQueryReq req);

    /**
     * 查询待结算总条数
     */
    Integer pendingPageListCount(SettlePendingQueryReq req);
}
