package com.juzifenqi.plus.module.order.model.contract.external;

import com.juzifenqi.plus.module.order.model.event.order.OrderRefundSecondEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderDefrayResultMqEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderMessageMqOutEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderMessageOutEvent;

/**
 * <AUTHOR>
 */
public interface IMessageExternalRepository {

    /**
     * 开通、取消、过期会员发送mq
     */
    void sendPlusMq(PlusOrderMessageMqOutEvent orderMessageOutEvent);

    /**
     * 发送消息给消息中心
     */
    void sendMessageCenter(PlusOrderMessageOutEvent orderMessageOutEvent);

    /**
     * 发送支付系统代付打款结果mq，内部消费。做会员取消操作
     */
    void sendDefrayResult(PlusOrderDefrayResultMqEvent event);

    /**
     * 发送会员开通消息
     * <a href="https://h16s2p02bsi.feishu.cn/wiki/Caz6wB7RQixcLskSkLecHzcxnFe">用户开通会员发送新MQ</a>
     * @param userId 用户id
     * @param configId 会员类型
     */
    void sendPlusOpenMq(Integer userId, Integer configId);

    /**
     * 首付支付退款，内部消息发起第二笔退款请求
     */
    void sendSecondRefundMq(OrderRefundSecondEvent event);
}
