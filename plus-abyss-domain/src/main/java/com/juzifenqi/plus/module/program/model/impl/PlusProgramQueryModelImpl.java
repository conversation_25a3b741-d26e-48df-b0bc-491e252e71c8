package com.juzifenqi.plus.module.program.model.impl;

import com.juzifenqi.plus.enums.AfterPaySateEnum;
import com.juzifenqi.plus.enums.BlackListTypeEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.module.common.IPlusBlackRepository;
import com.juzifenqi.plus.module.order.model.PlusOrderQueryModel;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.contract.IPlusLiftAmountRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusLmkVirtualRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProModelRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProductRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramCashbackRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramProductTypeRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramVirtualRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusRenewRepository;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusLiftAmountEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramCashbackEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramLmkVirtualEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramVirtualEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.repository.converter.PlusProgramConverter;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.po.PlusRenewRelevancePo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusLiftAmountPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductTypePo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.OptionalInt;
import java.util.stream.IntStream;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 方案信息查询model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/13 14:56
 */
@Slf4j
@Service
public class PlusProgramQueryModelImpl implements IPlusProgramQueryModel {

    @Autowired
    private IPlusLiftAmountRepository         plusLiftAmountRepository;
    @Autowired
    private IPlusProModelRepository           plusProModelRepository;
    @Autowired
    private IPlusProgramRepository            plusProgramRepository;
    @Autowired
    private IPlusProgramVirtualRepository     virtualRepository;
    @Autowired
    private IPlusLmkVirtualRepository         plusLmkVirtualRepository;
    @Autowired
    private PlusOrderQueryModel               orderQueryModel;
    @Autowired
    private IPlusBlackRepository              plusBlackRepository;
    @Autowired
    private IPlusRenewRepository              renewRepository;
    @Autowired
    private IPlusProductRepository            productRepository;
    @Autowired
    private IPlusProgramProductTypeRepository productTypeRepository;
    @Autowired
    private IPlusProgramCashbackRepository    cashbackRepository;

    private final PlusProgramConverter converter = PlusProgramConverter.instance;

    /**
     * 获取方案实体
     */
    @Override
    public PlusProgramEntity getById(Integer id) {
        return plusProgramRepository.getById(id);
    }

    /**
     * 获取订单的方案数据
     */
    @Override
    public PlusProgramEntity getProgramAndProfitsPackage(Integer id, String plusOrderSn) {
        // 获取会员订单信息
        PlusOrderEntity order = orderQueryModel.getByOrderSn(plusOrderSn);
        return plusProgramRepository.getProgramAndProfitsPackage(id, order);
    }

    @Override
    public int countProModelByProgramId(Integer programId, Integer modelId) {
        return plusProModelRepository.countProModelByProgramId(programId, modelId);
    }

    @Override
    public PlusLiftAmountEntity getByProgramId(Integer programId) {
        PlusLiftAmountPo plusLiftAmountPo = plusLiftAmountRepository.getByProgramId(programId);
        return converter.toPlusLiftAmountEntity(plusLiftAmountPo);
    }

    @Override
    public PlusProgramVirtualEntity getByProgramAndSku(Integer programId, Integer modelId,
            String sku) {
        PlusProgramVirtualPo po = virtualRepository.getByProgramAndSku(programId, modelId, sku);
        return converter.toPlusProgramVirtualEntity(po);
    }

    @Override
    public PlusProgramLmkVirtualEntity getVirtualById(Integer id) {
        return plusLmkVirtualRepository.getVirtualById(id);
    }

    @Override
    public List<PlusProgramLmkVirtualEntity> getLmkList(Integer programId) {
        return plusLmkVirtualRepository.getLmkList(programId);
    }

    @Override
    public PlusProgramVirtualTypePo getVirtualTypeById(Integer id) {
        return virtualRepository.getVirtualTypeById(id);
    }

    @Override
    public boolean supportAfterPay(Integer programId, Integer userId, Integer channelId) {
        PlusProgramEntity programEntity = this.supportPayTypes(programId, userId, channelId);
        return programEntity.getCheckFlag();
        /*PlusProgramEntity program = getById(programId);
        if (program == null) {
            log.info("是否支持后付款校验,方案不存在：{},{}", userId, programId);
            return false;
        }

        if (Objects.equals(program.getAfterPayState(), AfterPaySateEnum.NO_SUPPORT.getCode())) {
            log.info("是否支持后付款校验,方案不支持后付款：{},{}", userId, programId);
            return false;
        }
        List<PlusOrderEntity> waitPayOrderList = orderQueryModel.getUserWaitPayOrderList(userId,
                channelId);
        if (!CollectionUtils.isEmpty(waitPayOrderList)) {
            log.info("是否支持后付款校验,用户存在待支付后付款订单：{}", userId);
            return false;
        }
        boolean exist = plusBlackRepository.inBlackList(userId, program.getConfigId(),
                BlackListTypeEnum.BLACK_TYPE_1);
        if (exist) {
            log.info("是否支持后付款校验,用户在后付款黑名单内：{}", userId);
            return false;
        }
        return true;
        */
    }

    @Override
    public PlusProgramEntity supportPayTypes(Integer programId, Integer userId, Integer channelId) {
        PlusProgramEntity program = getById(programId);

        if (program == null || CollectionUtils.isEmpty(program.getPayTypes())) {
            log.info("是否支持后付款校验,方案不存在：{},{}", userId, programId);
            program = new  PlusProgramEntity();
            List<Integer> payTypes = new ArrayList<>();
            payTypes.add(PlusOrderPayTypeEnum.PAY_ALL.getValue());
            program.setPayTypes(payTypes);
            program.setCheckFlag(false);
            return program;
        }

        //查询后付款订单
        List<Integer> afterPayTypes = new ArrayList<>();
        afterPayTypes.add(PlusOrderPayTypeEnum.PAY_AFTER.getValue());
        List<PlusOrderEntity> waitPayOrderList = orderQueryModel.getUserWaitPayOrderListByCondition(userId,
                channelId, afterPayTypes);
        if (!CollectionUtils.isEmpty(waitPayOrderList)) {
            List<Integer> payTypes = new ArrayList<>();
            payTypes.add(PlusOrderPayTypeEnum.PAY_ALL.getValue());
            program.setPayTypes(payTypes);
            program.setCheckFlag(false);
            return program;
        }

        //查询首付支付订单
        List<Integer> firstPayTypes = new ArrayList<>();
        firstPayTypes.add(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue());
        List<PlusOrderEntity> firstPayOrderList = orderQueryModel.getUserWaitPayOrderListByCondition(userId,
                channelId,firstPayTypes);

        if (!CollectionUtils.isEmpty(firstPayOrderList)) {
            PlusOrderEntity plusOrderEntity = firstPayOrderList.stream().filter(order -> order.getPayAmount() != null && order.getPayAmount().compareTo(BigDecimal.ZERO) > 0).findFirst().orElse(null);
            if (plusOrderEntity != null) {
                List<Integer> payTypes = new ArrayList<>();
                payTypes.add(PlusOrderPayTypeEnum.PAY_ALL.getValue());
                program.setPayTypes(payTypes);
                program.setCheckFlag(false);
                return program;
            }
        }
        //黑名单
        boolean exist = plusBlackRepository.inBlackList(userId, program.getConfigId(),
                BlackListTypeEnum.BLACK_TYPE_1);
        if (exist) {
            log.info("是否支持后付款校验,用户在后付款黑名单内：{}", userId);
            List<Integer> payTypes = new ArrayList<>();
            payTypes.add(PlusOrderPayTypeEnum.PAY_ALL.getValue());
            program.setPayTypes(payTypes);
            program.setCheckFlag(false);
            return program;
        }

        program.setCheckFlag(true);
        return program;
    }

    @Override
    public PlusRenewRelevancePo getRenewRelevanceByProgramId(Integer programId) {
        return renewRepository.getByProgramId(programId);
    }

    @Override
    public List<PlusProgramEntity> getProgramByConfigId(Integer configId) {
        return plusProgramRepository.getProgramByConfigId(configId);
    }

    @Override
    public PlusProgramProductTypePo getProductType(String productSku, Integer programId,
            Integer modelId) {
        PlusProgramProductNewPo programProduct = productRepository.getProgramProduct(productSku,
                programId, modelId);
        if (programProduct == null || programProduct.getTypeId() == null) {
            return null;
        }
        return productTypeRepository.getProductType(programProduct.getTypeId());
    }

    @Override
    public BigDecimal getSettleCashbackAmount(Integer configId, Integer programId) {
        List<PlusProgramCashbackEntity> list = cashbackRepository.getByProgramIdAndModelId(
                programId, PlusModelEnum.JQFX.getModelId());
        return CollectionUtils.isEmpty(list) ? null : list.get(0).getCashbackAmount();
    }

    @Override
    public List<PlusProModelEntity> getPlusProModelList(Integer programId) {
        return plusProModelRepository.getPlusProModelList(programId);
    }
}
