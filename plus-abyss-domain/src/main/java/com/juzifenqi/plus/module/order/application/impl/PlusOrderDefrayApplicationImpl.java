package com.juzifenqi.plus.module.order.application.impl;

import com.juzifenqi.plus.module.order.application.IPlusOrderDefrayApplication;
import com.juzifenqi.plus.module.order.application.validator.PlusOrderDefrayValidator;
import com.juzifenqi.plus.module.order.model.IPlusOrderDefrayModel;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.defray.PayDefrayResultEntity;
import com.juzifenqi.plus.module.order.model.event.order.CreateDefrayApplyEvent;
import com.juzifenqi.plus.module.order.model.event.order.OrderRefundSecondEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 订单代付
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 14:24
 */
@Service
@Slf4j
public class PlusOrderDefrayApplicationImpl implements IPlusOrderDefrayApplication {

    @Autowired
    private IPlusOrderDefrayModel    defrayModel;
    @Autowired
    private PlusOrderDefrayValidator validator;

    @Override
    public void createDefrayApply(CreateDefrayApplyEvent event) {
        // 校验
        PlusOrderEntity plusOrder = validator.createDefrayApplyValidator(event);
        // 创建代付申请
        defrayModel.createDefrayApply(event, plusOrder);
    }

    @Override
    public void orderDefrayExecute() {
        defrayModel.orderDefrayExecute();
    }

    @Override
    public void orderDefrayPayResult(PayDefrayResultEntity result) {
        defrayModel.orderDefrayPayResult(result);
    }

    @Override
    public void changeCardDefrayPayRefund(OrderRefundNotifyEntity entity) {
        defrayModel.changeCardDefrayPayRefund(entity);
    }

    @Override
    public void defrayPayRefundSecond(OrderRefundSecondEvent event) {
        defrayModel.defrayPayRefundSecond(event);
    }

    @Override
    public void initPastCardNoUuid(Long index, Integer size) {
        defrayModel.initPastCardNoUuid(index, size);
    }

    @Override
    public void inspectPastCardNoUuid(Integer size) {
        defrayModel.inspectPastCardNoUuid(size);
    }
}
