package com.juzifenqi.plus.module.order.repository.external.acl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.core.date.DateUtils;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.alita.result.BaseResult;
import com.juzifenqi.core.ServiceResult;
import com.juzifenqi.enumeration.OrderChannelEnum;
import com.juzifenqi.enumeration.OrderStateEnum;
import com.juzifenqi.oms.api.OmsResult;
import com.juzifenqi.oms.bean.OmsLoanParams;
import com.juzifenqi.oms.bean.OrderBase;
import com.juzifenqi.order.constants.OrderTypeEnum;
import com.juzifenqi.order.dao.entity.Orders;
import com.juzifenqi.order.dao.entity.OrdersBase;
import com.juzifenqi.order.dao.entity.OrdersResubmitRecord;
import com.juzifenqi.order.dto.OrderQueryDTO;
import com.juzifenqi.order.dto.OrderSimpleInfoDTO;
import com.juzifenqi.order.vo.OrderCancelRefundResultVO;
import com.juzifenqi.order.vo.OrderCancelRefundVO;
import com.juzifenqi.order.vo.OrderExtraVO;
import com.juzifenqi.order.vo.OrderQueryVO;
import com.juzifenqi.order.vo.product.OrderProductVo;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.pojo.CreateOrderContext;
import com.juzifenqi.plus.enums.CancelReasonEnum;
import com.juzifenqi.plus.enums.PlusCancelTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.enums.VipErrorEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.external.OrderExternal;
import com.juzifenqi.plus.module.common.IFmsRepository;
import com.juzifenqi.plus.module.common.IIMRepository;
import com.juzifenqi.plus.module.common.entity.UserCardNewEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.OrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderCancelEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.UpdOrderStateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualOrderCreateEvent;
import com.juzifenqi.plus.utils.RedisUtils;
import com.juzifenqi.vo.MemberVipVO;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderExternalRepositoryAcl implements IOrderExternalRepository {


    @Autowired
    private OrderExternal  orderExternal;
    @Autowired
    private RedisUtils     redisUtils;
    @Autowired
    private IFmsRepository userCardRepository;
    @Autowired
    private IIMRepository iimRepository;

    /**
     * 0元订单/先支付后创单的订单标识
     */
    private final String ORDER_SOURCE = "MEMBER_HELLO";

    /**
     * 请求订单服务提交订单
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2020/12/8 2:52 下午
     */
    @Override
    public OrderEntity emberOrderCommit(PlusOrderEntity order, PlusOrderCreateEvent event) {
        //请求订单
        Integer channelId = order.getChannelId();
        MemberVipVO memberVipVO = new MemberVipVO();
        memberVipVO.setUserId(order.getUserId());
        memberVipVO.setChannelId(channelId);
        memberVipVO.setVipType(String.valueOf(order.getProgramId()));
        memberVipVO.setVipPrice(order.getOrderAmount());
        memberVipVO.setMoney(order.getOrderAmount());
        memberVipVO.setQuantity(1);
        PlusOrderPayTypeEnum payTypeEnum = PlusOrderPayTypeEnum.getByValue(order.getPayType());
        if (payTypeEnum == null) {
            throw new PlusAbyssException("无效的付款类型");
        }
        // 处理订单类型
        if (payTypeEnum.getPayAfter()) {
            memberVipVO.setOperateType(String.valueOf(OrderTypeEnum.会员后付款订单.getCode()));
        } else if (PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.equals(payTypeEnum)) {
            // 分期支付订单类型设置为1205
            memberVipVO.setOperateType(String.valueOf(OrderTypeEnum.会员首付款订单.getCode()));
            // 设置首付金额
            if (order.getFirstPayAmount() != null) {
                memberVipVO.setDownpaymentAmount(order.getFirstPayAmount());
            }
        } else {
            memberVipVO.setOperateType(String.valueOf(OrderTypeEnum.商城普通订单.getCode()));
        }
        // 20231122 zjf 非商城渠道 & 开通方式=全款 & 支付方式=划扣，给订单中心特殊标识、支付通道、支付商编、支付流水号
        CreateOrderContext createOrderContext = event.getCreateOrderContext();
        Integer payType = createOrderContext != null ? createOrderContext.getPayType() : null;
        Integer openType = event.getPayType();
        if (openType != null && openType == CommonConstant.ONE && payType != null
                && payType == CommonConstant.ONE && channelId != 1) {
            memberVipVO.setThirdPartySources(ORDER_SOURCE);
            memberVipVO.setPaymentSerialNumber(createOrderContext.getSerialNumber());
            memberVipVO.setPaymentChannel(createOrderContext.getPayChannel());
            memberVipVO.setMerchantNo(createOrderContext.getPayMerchantCode());
        }
        // huxf 2023.12.08 新增订单归因字段
        memberVipVO.setAscribeTo(order.getAscribeTo());
        log.info("请求订单服务提交订单入参：{}", JSON.toJSONString(memberVipVO));
        ServiceResult<Map<String, String>> serviceResult = orderExternal.emberOrderCommit(
                memberVipVO);
        log.info("请求订单服务提交订单返回数据：{}", JSONObject.toJSONString(serviceResult));
        if (!serviceResult.getSuccess() || serviceResult.getResult() == null) {
            throw new PlusAbyssException("请求订单服务异常，请稍后再试！");
        }
        Map<String, String> result = serviceResult.getResult();
        OrderEntity orderEntity = new OrderEntity();
        orderEntity.setOrderId(result.get("orderId"));
        orderEntity.setSign(result.get("sign"));
        orderEntity.setMoneyOrder(new BigDecimal(result.get("moneyOrder")));
        //后付款订单记录订单号
        setAfterOrderToRedis(order, orderEntity, payTypeEnum);
        return orderEntity;
    }

    @Override
    public void closeOrder(PlusOrderCancelEvent event, PlusOrderEntity order,
            PlusOrderCancelEntity cancel) {
        try {
            String orderSn = order.getOrderSn();
            Integer cancelType = event.getCancelType();
            String cancelReason = CancelReasonEnum.getNameByCode(event.getCancelReason());
            // 全额退款或待支付后付款订单(非代付取消)，走原路退款
            if ((cancel.getMoneyBack().compareTo(order.getOrderAmount()) == 0 || (
                    Objects.equals(order.getPayType(), PlusOrderPayTypeEnum.PAY_AFTER.getValue())
                            && order.getOrderState() == PlusOrderStateEnum.WAIT_PAY.getCode()))
                    && PlusCancelTypeEnum.isNotDefrayCancel(cancelType)) {
                log.info("全额或待支付后付款退款调用取消订单开始：{}", orderSn);
                Boolean result = orderExternal.cancelOrderForCusys(orderSn, cancelReason,
                        event.getOptUserName());
                log.info("全额或待支付后付款退款调用取消订单返回：{}", result);
                if (result == null || Boolean.FALSE.equals(result)) {
                    throw new PlusAbyssException("全额退款订单失败");
                }
                return;
            }
            // 部分退（非代付取消），走原路退款
            if (cancel.getMoneyBack().compareTo(BigDecimal.ZERO) > 0
                    && cancel.getMoneyBack().compareTo(order.getOrderAmount()) < 0
                    && PlusCancelTypeEnum.isNotDefrayCancel(cancelType)) {
                log.info("部分退款调用取消订单开始：{},{}", orderSn, cancel.getMoneyBack());
                Boolean result = orderExternal.cancelOrderPartRefund(orderSn, cancelReason,
                        event.getOptUserName(), event.getOptUserId(), cancel.getMoneyBack());
                log.info("部分退款调用取消订单返回：{}", result);
                if (result == null || Boolean.FALSE.equals(result)) {
                    throw new PlusAbyssException("取消部分退款订单失败");
                }
                return;
            }
            // 退0元或代付取消，不走原路退款，只取消订单
            if (cancel.getMoneyBack().compareTo(BigDecimal.ZERO) == 0
                    || !PlusCancelTypeEnum.isNotDefrayCancel(cancelType)) {
                log.info("0元或代付取消调用取消订单开始,orderSn: {}", orderSn);
                ServiceResult<Boolean> result = orderExternal.closePlusOrder(orderSn);
                log.info("0元或代付取消调用取消订单结束返回: {}", JSON.toJSONString(result));
                if (result.getResult() == null || Boolean.FALSE.equals(result.getResult())) {
                    throw new PlusAbyssException("取消0元订单失败");
                }
            }
        } catch (Exception e) {
            LogUtil.printLog(e, "调用订单中心取消订单异常");
            if (e instanceof PlusAbyssException) {
                throw e;
            }
            throw new PlusAbyssException("订单中心取消失败");
        }
    }

    @Override
    public void customerUpdateOrderState(UpdOrderStateEvent event) {
        log.info("客服变更订单状态入参：{}", JSON.toJSONString(event));
        String reason = CancelReasonEnum.getNameByCode(event.getChangeReasonCode());
        BaseResult<Boolean> result = orderExternal.customerUpdateOrderState(event.getOrderSn(),
                event.getOrderState(), reason, event.getRefundAmount(), event.getOptUserId(),
                event.getOptUserName());
        log.info("客服变更订单状态返回：{}", JSON.toJSONString(result));
        if (result.getCode() != 200 || result.getData() == null || !result.getData()) {
            throw new PlusAbyssException("修改订单中心状态失败：" + result.getMessage());
        }
    }


    @Override
    public OrderSimpleInfoDTO getByOrderSn(String orderSn) {
        log.info("调用订单服务获取订单基本信息入参为{}", orderSn);
        ServiceResult<OrderSimpleInfoDTO> commonDataBySn = orderExternal.getCommonDataBySn(orderSn);

        log.info("当前订单信息为{}", JSONObject.toJSONString(commonDataBySn));
        if (commonDataBySn.getResult() != null) {
            return commonDataBySn.getResult();
        }
        return null;
    }

    /**
     * 获取借款单的银行信息
     */
    @Override
    public UserCardNewEntity getBankInfoByLoanOrderSn(String loanOrderSn) {
        OrderSimpleInfoDTO orderSimpleInfoDTO = getByOrderSn(loanOrderSn);
        if (orderSimpleInfoDTO == null) {
            log.error("获取借款单失败，订单的银行信息返回null, loanOrderSn = {}", loanOrderSn);
            return null;
        }
        return userCardRepository.getBankByIdV2(orderSimpleInfoDTO.getMemberId(),
                orderSimpleInfoDTO.getBankId(), String.valueOf(orderSimpleInfoDTO.getUserChannel()));
    }

    /**
     * 调订单服务更改订单状态
     */
    @Override
    public void cancelOrderForCusys(String orderSn, String reason, String operatorName) {
        try {
            log.info("请求订单服务更改订单状态参数orderSn:{},reason:{},operatorName:{}", orderSn,
                    reason, operatorName);
            Boolean result = orderExternal.cancelOrderForCusys(orderSn, reason, operatorName);
            log.info("请求订单服务更改订单状态结束:{}", result);
        } catch (Exception e) {
            LogUtil.printLog("请求订单服务更改订单状态异常", e);
            throw new PlusAbyssException(VipErrorEnum.PLUS_ERROR_700016);
        }
    }

    @Override
    public OrderEntity virtualOrderCommit(VirtualOrderCreateEvent event) {
        MemberVipVO memberVipVO = new MemberVipVO();
        memberVipVO.setUserId(event.getUserId());
        memberVipVO.setChannelId(event.getChannelId());
        memberVipVO.setVipType(String.valueOf(event.getProgramId()));
        memberVipVO.setMoney(event.getOrderAmount());
        memberVipVO.setQuantity(CommonConstant.ONE);
        memberVipVO.setProductSku(event.getProductSku());
        // 20231122 zjf 0元订单给标识
        if (event.getOrderAmount().compareTo(BigDecimal.ZERO) == 0) {
            memberVipVO.setThirdPartySources(ORDER_SOURCE);
        }
        log.info("会员虚拟商品提交订单,参数:{}", JSONObject.toJSONString(memberVipVO));
        ServiceResult<Map<String, String>> serviceResult = orderExternal.virtualOrderCommit(
                memberVipVO);
        log.info("会员虚拟商品提交订单返回数据：{}",
                JSONObject.toJSONString(serviceResult.getResult()));
        if (!serviceResult.getSuccess() || serviceResult.getResult() == null) {
            throw new PlusAbyssException("虚拟商品下单服务异常，请稍后再试！");
        }
        Map<String, String> result = serviceResult.getResult();
        OrderEntity order = new OrderEntity();
        order.setOrderId(result.get("orderId"));
        order.setMoneyOrder(new BigDecimal(result.get("moneyOrder")));
        return order;
    }

    /**
     * 设置后付款开通的会员到缓存，如果后期开通会员失败存储到plus_invalid_order表时会进行校验缓存是否存在
     *
     * <AUTHOR>
     * @date 2023/8/9 21:35
     **/
    private void setAfterOrderToRedis(PlusOrderEntity orderDto, OrderEntity order,
            PlusOrderPayTypeEnum payTypeEnum) {
        if (payTypeEnum.getPayAfter()) {
            log.info("会员后付款下单成功后保存回滚标识：orderSn：{}", order.getOrderId());
            //后付款记录订单号
            String redisKey =
                    RedisConstantPrefix.PLUS_MEMBER_AFTER_ORDER + orderDto.getUserId() + "_"
                            + orderDto.getProgramId();
            redisUtils.setEx(redisKey, order.getOrderId(), 15, TimeUnit.SECONDS);
        }
    }

    /**
     * 查询订单基础信息和扩展信息
     *
     * <AUTHOR>
     * @date 2023/12/18 16:01
     **/
    @Override
    public OrderExtraVO getBaseAndExtraByOrderSn(String orderSn) {
        log.info("调用订单服务获取订单基本信息和扩展信息入参为{}", orderSn);
        ServiceResult<OrderExtraVO> commonDataBySn = orderExternal.getCommonDataAndExtraBySn(
                orderSn);

        log.info("当前订单(基本+扩展)信息为{}", JSONObject.toJSONString(commonDataBySn));
        if (commonDataBySn != null && commonDataBySn.getResult() != null) {
            return commonDataBySn.getResult();
        }
        return null;
    }

    @Override
    public String getOrderStateDesc(Integer orderState) {
        for (OrderStateEnum value : OrderStateEnum.values()) {
            if (Objects.equals(orderState, value.getState())) {
                return value.getStateDesc();
            }
        }
        return null;
    }

    /**
     * 订单撞库校验
     **/
    @Override
    public Boolean checkStrikeBase(Integer userId) {
        return orderExternal.checkStrikeBase(userId);
    }

    @Override
    public void invalidOrder(List<String> orderSns) {
        log.info("失效订单入参：{}", orderSns);
        ServiceResult<Integer> result = orderExternal.invalidOrder(orderSns);
        log.info("失效订单返回：{}", JSON.toJSONString(result));
    }

    @Override
    public Orders lastOneProcessOrder(Integer userId) {
        try {
            log.info("请求订单服务查最近一笔611或501的借款订单参数memberId:{}", userId);
            Orders result = orderExternal.lastOneProcessOrder(userId).getResult();
            log.info("请求订单服务查最近一笔611或501的借款订单返回result:{}",
                    JSONObject.toJSONString(result));
            return result;
        } catch (Exception e) {
            LogUtil.printLog("请求订单服务查最近一笔611放款中的借款订单异常", e);
            return null;
        }
    }

    @Override
    public void closeUnpayOrders(Integer userId, Integer channel) {
        try {
            log.info("关闭用户待支付的订单-入参：{},{}", userId, channel);
            ServiceResult<Boolean> result = orderExternal.closeUnpayOrders(userId, channel);
            log.info("关闭用户待支付的订单-返回：{}", JSONObject.toJSONString(result));
        } catch (Exception e) {
            LogUtil.printLog(e, "关闭用户待支付的订单异常");
            throw new PlusAbyssException("关闭用户待支付的订单异常");
        }
    }

    @Override
    public void changeOrderRate(String orderSn, BigDecimal rate) {
        try {
            log.info("通知订单更改利率，入参：{},{}", orderSn, rate);
            BaseResult<Boolean> result = orderExternal.changeOrderRate(orderSn, rate);
            log.info("通知订单更改利率，返回：{}", JSONObject.toJSONString(result));
        } catch (Exception e) {
            LogUtil.printLog(e, "通知订单更改利率异常");
            throw new PlusAbyssException(VipErrorEnum.PLUS_ERROR_800014);
        }
    }

    @Override
    public OrdersResubmitRecord getUserResubmitOrder(String newOrderSn) {
        try {
            log.info("根据新订单号请求订单服务查询重提订单入参：{}", newOrderSn);
            ServiceResult<OrdersResubmitRecord> result = orderExternal.getUserResubmitOrder(
                    newOrderSn);
            log.info("根据新订单号请求订单服务查询重提订单结果：{}",
                    JSONObject.toJSONString(result));
            if (result != null) {
                return result.getResult();
            }
            return null;
        } catch (Exception e) {
            LogUtil.printLog("根据新订单号请求订单服务查询重提订单异常", e);
            throw new PlusAbyssException(VipErrorEnum.PLUS_ERROR_700015);
        }
    }

    /**
     * 获取用户所有的购买半价商品记录
     *
     * @param memberId 用户id
     * @param jxStartTime 开始时间
     * @param jxEndTime 结束时间
     * @param productIds 商品id集合
     */
    public List<OrderProductVo> getAllHalfProductOrder(Integer memberId, Date jxStartTime,
            Date jxEndTime, List<Integer> productIds) {
        try {
            log.info(
                    "获取用户所有的购买半价商品记录入参：memberId-{},jxStartTime-{},jxEndTime-{},productIds-{}",
                    memberId, jxStartTime, jxEndTime, JSON.toJSONString(productIds));
            ServiceResult<List<OrderProductVo>> result = orderExternal.getOrdersBaseByProductList(
                    memberId, jxStartTime, jxEndTime, productIds);
            log.info("获取用户所有的购买半价商品记录返回：{}", JSON.toJSONString(result));
            if (result == null || !result.getSuccess()) {
                throw new PlusAbyssException("获取用户所有的购买半价商品记录失败！");
            }
            return result.getResult();
        } catch (PlusAbyssException e) {
            LogUtil.printLog("获取用户所有的购买半价商品记录业务异常,memberId-{}", memberId, e);
            throw e;
        } catch (Exception e) {
            LogUtil.printLog("获取用户所有的购买半价商品记录未知异常,memberId-{}", memberId, e);
            throw new PlusAbyssException("获取用户所有的购买半价商品记录未知异常");
        }
    }

    @Override
    public List<OrdersBase> queryOrdersBase(Integer outUserId, List<Integer> statusList,
            Date createTime, Date endTime, Integer channel) {
        OrderQueryVO queryVO = new OrderQueryVO();
        queryVO.setMemberId(outUserId);
        queryVO.setApplyTime(DateUtils.getDateTime(createTime));
        queryVO.setEndTime(DateUtils.getDateTime(endTime));
        queryVO.setOrderStateList(statusList);
        queryVO.setChannel(channel);
        log.info("时间范围-查询指定用户订单信息入参：{}", JSON.toJSONString(queryVO));
        ServiceResult<List<OrdersBase>> result = orderExternal.getOrderByMemberId(queryVO);
        log.info("时间范围-查询指定用户订单信息出参：{}", JSON.toJSONString(result));
        if (result == null || !result.getSuccess()) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100004.getCode().toString(),
                    "时间范围-查询指定用户订单信息返回为空，请稍后再试！");
        }
        return result.getResult();
    }

    @Override
    public List<OrderQueryDTO> getOrderByUserId(Integer memberId) {
        OrderQueryVO orderQueryVO = new OrderQueryVO();
        orderQueryVO.setOrderStateList(Arrays.asList(OrderStateEnum.待签约.getState(),
                OrderStateEnum.交易复核中.getState(), OrderStateEnum.备货中.getState()));
        orderQueryVO.setChannel(OrderChannelEnum.现金贷.getChannelCode());
        orderQueryVO.setMemberId(memberId);
        log.info("查询在途订单入请求参数:{}", JSON.toJSONString(orderQueryVO));
        ServiceResult<List<OrderQueryDTO>> result = orderExternal.getOrderByMemberIdAndChannel(
                orderQueryVO);
        log.info("查询在途订单入返回结果：{}", JSON.toJSONString(result));
        if (result == null || !result.getSuccess()) {
            log.info("查询在途订单返回失败：{}", memberId);
            throw new PlusAbyssException("查询在途订单失败");
        }
        return result.getResult();
    }

    @Override
    public List<OrderQueryDTO> getOnWayOrderByUserId(Integer memberId) {
        OrderQueryVO orderQueryVO = new OrderQueryVO();
        orderQueryVO.setOrderStateList(Arrays.asList(OrderStateEnum.待信用支付.getState(), OrderStateEnum.交易复核中.getState(),
                OrderStateEnum.备货中.getState(), OrderStateEnum.赋强公证屯单中.getState()));
        orderQueryVO.setChannel(OrderChannelEnum.现金贷.getChannelCode());
        orderQueryVO.setMemberId(memberId);
        log.info("getOnWayOrderByUserId查询在途订单入请求参数:{}", JSON.toJSONString(orderQueryVO));
        ServiceResult<List<OrderQueryDTO>> result = orderExternal.getOrderByMemberIdAndChannel(
                orderQueryVO);
        log.info("getOnWayOrderByUserId查询在途订单入返回结果：{}", JSON.toJSONString(result));
        if (result == null || !result.getSuccess()) {
            log.info("getOnWayOrderByUserId查询在途订单返回失败：{}", memberId);
            throw new PlusAbyssException("getOnWayOrderByUserId查询在途订单失败");
        }
        return result.getResult();
    }

    @Override
    public OrdersBase getOrdersBaseByOrdersNo(String orderSn) {
        log.info("调用订单服务获取订单基本信息入参为{}", orderSn);
        ServiceResult<OrdersBase> commonDataBySn = orderExternal.getBaseCommonDataBySn(orderSn);
        log.info("当前订单信息为{}", JSON.toJSONString(commonDataBySn));
        if (commonDataBySn.getResult() != null) {
            return commonDataBySn.getResult();
        }
        return null;
    }

    @Override
    public OrderBase orderSubmit(OmsLoanParams orderSubmitVo) {
        try {
            log.info("调用订单服务创单入参为：{}", JSON.toJSONString(orderSubmitVo));
            OmsResult<OrderBase> orderBaseOmsResult = orderExternal.orderSubmit(orderSubmitVo);
            log.info("调用订单服务创单返回为：{}", JSON.toJSONString(orderBaseOmsResult));
            if (orderBaseOmsResult == null || !orderBaseOmsResult.isSuccess()) {
                log.info("创单接口返回为空，请稍后再试！");
                return null;
            }
            return orderBaseOmsResult.getResult();
        } catch (Exception e) {
            LogUtil.printLog("调用订单接口创单失败", e);
            return null;
        }
    }

    @Override
    public Orders lastOneLoanOrder(Integer memberId) {
        try {
            log.info("请求订单服务查最近一笔611放款中的借款订单参数:{}", memberId);
            ServiceResult<Orders> result = orderExternal.lastOneLoanOrder(memberId);
            log.info("请求订单服务查最近一笔611放款中的借款订单返回:{}",
                    JSONObject.toJSONString(result));
            return result.getResult();
        } catch (Exception e) {
            LogUtil.printLog("请求订单服务查最近一笔611放款中的借款订单异常", e);
        }
        return null;
    }

    @Override
    public Orders getOrdersBySn(String orderSn) {
        try {
            log.info("调用订单中心获取订单详情入参：{}", orderSn);
            ServiceResult<Orders> result = orderExternal.getOrdersBySn(orderSn);
            log.info("调用订单中心获取订单详情返回：{}", JSON.toJSONString(result));
            if (!result.getSuccess() || result.getResult() == null) {
                return null;
            }
            return result.getResult();
        } catch (Exception e) {
            LogUtil.printLog(e, "调用订单中心获取订单详情异常");
        }
        return null;
    }

    @Override
    public OrderCancelRefundResultVO closeOrderRefund(OrderCancelRefundVO vo) {
        try {
            log.info("调用订单中心取消订单入参为：{}", JSON.toJSONString(vo));
            BaseResult<OrderCancelRefundResultVO> result = orderExternal.closeOrderRefund(vo);
            log.info("调用订单中心取消订单返回为：{}", JSON.toJSONString(result));
            if (result.getCode() != 200 || result.getData() == null) {
                log.info("调用订单中心取消订单返回结果错误,请稍后再试!");
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            LogUtil.printLog("调用订单中心取消订单异常", e);
            // 发飞书
            iimRepository.sendImMessage("调用订单中心取消订单异常,订单号:" + vo.getOrderSn());
            return null;
        }
    }
}
